# 年度预算页面月份点击跳转到预算列表功能实现

## 功能概述

根据用户需求，修改了年度预算详情页面的月份点击跳转逻辑。现在当用户在年度预算详情页面点击某个月份时，会跳转到预算列表页面，并自动筛选显示该月份的预算数据，同时保持当前选择的年份和类别筛选条件。

## 修改内容

### 1. 年度预算页面 (`Annual.vue`) 修改

**更新的 `viewMonthDetail` 方法：**
```javascript
const viewMonthDetail = (month) => {
  // 构建路由参数，保持当前的筛选条件
  const year = selectedYear.value === 'all' ? new Date().getFullYear() : selectedYear.value
  const category = selectedCategory.value === 'all' ? 'all' : selectedCategory.value
  
  // 跳转到预算列表页面，并传递筛选参数
  router.push({
    name: 'BudgetsList',
    query: {
      year: year.toString(),
      month: month.month.toString(),
      category: category !== 'all' ? category : undefined,
      sort: 'date_asc'
    }
  })
}
```

**主要变更：**
- 跳转目标从 `MonthlyBudget` 改为 `BudgetsList`
- 使用 `query` 参数而不是 `params` 传递筛选条件
- 只有非"全部"类别时才传递 category 参数

### 2. 预算列表页面 (`List.vue`) 增强

#### 2.1 新增功能
- **类别筛选**：添加了类别筛选下拉菜单
- **路由参数支持**：支持通过URL查询参数接收筛选条件
- **动态筛选**：监听路由变化自动更新筛选条件

#### 2.2 新增响应式数据
```javascript
const selectedCategory = ref('')  // 新增类别筛选
```

#### 2.3 新增计算属性
```javascript
// 获取可用的类别选项
const categoryOptions = computed(() => {
  const categories = new Set()
  budgets.value.forEach(budget => {
    if (budget.category) {
      categories.add(budget.category)
    }
  })
  const categoryArray = Array.from(categories).sort()
  return [
    { text: '全部类别', value: '' },
    ...categoryArray.map(category => ({ text: category, value: category }))
  ]
})
```

#### 2.4 路由参数初始化函数
```javascript
const initializeFiltersFromRoute = () => {
  // 月份参数
  if (route.query.year && route.query.month) {
    const year = route.query.year
    const month = String(route.query.month).padStart(2, '0')
    selectedMonth.value = `${year}年${month}月`
  }
  
  // 类别参数
  if (route.query.category) {
    selectedCategory.value = route.query.category
  }
  
  // 排序参数
  if (route.query.sort) {
    sortType.value = route.query.sort
  }
}
```

#### 2.5 增强的筛选逻辑
```javascript
const sortedAndFilteredBudgets = computed(() => {
  let result = [...budgets.value]

  // 月份筛选
  if (selectedMonth.value) {
    result = result.filter(budget => {
      const budgetMonth = getBudgetMonth(budget.start_date)
      return budgetMonth === selectedMonth.value
    })
  }

  // 类别筛选（新增）
  if (selectedCategory.value) {
    result = result.filter(budget => {
      return budget.category === selectedCategory.value
    })
  }

  // 排序逻辑...
  return result
})
```

#### 2.6 模板更新
```html
<!-- 筛选和排序 -->
<van-dropdown-menu>
  <van-dropdown-item v-model="selectedMonth" :options="monthOptions" title="全部月份" />
  <van-dropdown-item v-model="selectedCategory" :options="categoryOptions" title="全部类别" />
  <van-dropdown-item v-model="sortType" :options="sortOptions" title="默认排序" />
</van-dropdown-menu>
```

## 使用流程

### 1. 从年度预算页面跳转
1. 用户访问年度预算详情页面：`/budgets/annual`
2. 选择年份和类别筛选条件（可选）
3. 点击任意月份行
4. 自动跳转到预算列表页面：`/budgets/list?year=2025&month=3&category=餐饮&sort=date_asc`
5. 预算列表页面自动应用筛选条件，显示对应月份和类别的预算

### 2. URL示例
- 基本跳转：`/budgets/list?year=2025&month=1&sort=date_asc`
- 包含类别：`/budgets/list?year=2025&month=3&category=餐饮&sort=date_asc`
- 全部类别：`/budgets/list?year=2025&month=6&sort=date_asc`

### 3. 筛选条件保持
- **年份**：从年度预算页面的年份选择器传递
- **月份**：从点击的月份行传递
- **类别**：从年度预算页面的类别选择器传递（如果不是"全部"）
- **排序**：默认按日期升序排列

## 技术实现细节

### 1. 路由参数处理
- 使用 `useRoute` 获取查询参数
- 在 `onMounted` 时初始化筛选条件
- 使用 `watch` 监听路由变化

### 2. 数据格式转换
- 年份和月份组合为 "YYYY年MM月" 格式
- 类别直接使用原始值
- 排序参数直接应用

### 3. 用户体验优化
- 智能的空状态描述
- 清除筛选器功能
- 动态的筛选选项生成

## 兼容性说明

- 保持了原有的预算列表功能
- 新增的路由参数是可选的
- 向后兼容，不影响直接访问预算列表页面
- 支持手动修改URL参数

## 测试建议

1. **基本跳转测试**：从年度预算页面点击不同月份
2. **筛选条件测试**：验证年份、类别筛选是否正确传递
3. **URL直接访问测试**：直接通过URL访问带参数的预算列表
4. **清除筛选测试**：验证清除筛选功能是否正常
5. **空状态测试**：测试没有数据时的显示效果

## 后续优化建议

1. **面包屑导航**：添加面包屑显示当前筛选路径
2. **筛选历史**：记住用户的筛选偏好
3. **快速筛选**：添加常用筛选条件的快捷按钮
4. **数据缓存**：优化重复访问的性能
