<template>
  <div class="budget-list-page">
    <van-nav-bar
      title="预算列表"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <van-icon name="plus" size="18" @click="addBudget" />
      </template>
    </van-nav-bar>

    <div class="page-container">
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>

      <div v-else>
        <!-- 筛选和排序 -->
        <van-dropdown-menu>
          <van-dropdown-item v-model="selectedMonth" :options="monthOptions" title="全部月份" />
          <van-dropdown-item v-model="selectedCategory" :options="categoryOptions" title="全部类别" />
          <van-dropdown-item v-model="sortType" :options="sortOptions" title="默认排序" />
        </van-dropdown-menu>

        <!-- 预算状态提醒 -->
        <div v-if="filteredWarningBudgets.length > 0 || filteredExceededBudgets.length > 0" class="budget-alerts">
          <van-notice-bar
            v-if="filteredExceededBudgets.length > 0"
            left-icon="warning-o"
            color="#ee0a24"
            background="#fef0f0"
            :text="`${filteredExceededBudgets.length}个预算已超支`"
            @click="showExceededBudgets"
          />
          <van-notice-bar
            v-if="filteredWarningBudgets.length > 0"
            left-icon="info-o"
            color="#ff976a"
            background="#fff7cc"
            :text="`${filteredWarningBudgets.length}个预算接近上限`"
            @click="showWarningBudgets"
          />
        </div>

        <!-- 预算列表 -->
        <div class="budget-list">
          <div v-if="sortedAndFilteredBudgets.length === 0" class="empty-container">
            <van-empty
              image="search"
              :description="getEmptyDescription()"
            >
              <van-button v-if="!selectedMonth && !selectedCategory" type="primary" size="small" @click="addBudget">
                创建第一个预算
              </van-button>
              <van-button v-else type="default" size="small" @click="clearFilter">
                查看所有预算
              </van-button>
            </van-empty>
          </div>

          <!-- 预算卡片列表 -->
          <div v-else class="budget-cards">
            <div
              v-for="budget in sortedAndFilteredBudgets"
              :key="budget.id"
              class="budget-card"
              :class="budget.status"
              @click="viewBudget(budget)"
            >
              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="budget-info">
                  <div class="budget-icon" :class="budget.status">
                    {{ getCategoryIcon(budget.category) }}
                  </div>
                  <div class="budget-details">
                    <h4 class="project-name">{{ budget.project_name || budget.category }}</h4>
                    <p class="budget-meta">
                      <span class="category-tag">{{ budget.category }}</span>
                      <span class="period-text">{{ formatMonth(budget.start_date, budget.end_date) }}</span>
                    </p>
                  </div>
                </div>
                <div class="status-indicator" :class="budget.status">
                  {{ getStatusText(budget.status) }}
                </div>
              </div>

              <!-- 预算进度 -->
              <div class="budget-progress">
                <div class="progress-header">
                  <span class="used-amount">已用 {{ formatCurrency(budget.used_amount || 0) }}</span>
                  <span class="total-amount">总计 {{ formatCurrency(budget.amount) }}</span>
                </div>
                <div class="progress-bar-container">
                  <van-progress
                    :percentage="Math.min(budget.usage_percentage || 0, 100)"
                    :color="getProgressColor(budget.usage_percentage || 0)"
                    :show-pivot="false"
                    stroke-width="6"
                    class="progress-bar"
                  />
                  <span class="progress-percentage">{{ Math.round(budget.usage_percentage || 0) }}%</span>
                </div>
                <div class="remaining-info">
                  <span class="remaining-amount">
                    剩余 {{ formatCurrency(budget.amount - (budget.used_amount || 0)) }}
                  </span>
                  <span class="remaining-days">{{ getRemainingDays(budget) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useBudgetsStore } from '@/stores/budgets'
import { useTransactionsStore } from '@/stores/transactions'
import { formatCurrency } from '@/utils/format'
import { showToast } from 'vant'

const router = useRouter()
const route = useRoute()
const budgetsStore = useBudgetsStore()
const transactionsStore = useTransactionsStore()

// 响应式数据
const loading = ref(false)
const selectedMonth = ref('')
const selectedCategory = ref('')
const sortType = ref('date_asc')

// 从路由参数初始化筛选条件
const initializeFiltersFromRoute = () => {
  // 检查路由查询参数
  if (route.query.year && route.query.month) {
    const year = route.query.year
    const month = String(route.query.month).padStart(2, '0')
    selectedMonth.value = `${year}年${month}月`
  }

  // 如果有类别参数
  if (route.query.category) {
    selectedCategory.value = route.query.category
  }

  // 如果有排序参数
  if (route.query.sort) {
    sortType.value = route.query.sort
  }
}

// 计算属性
const budgets = computed(() => budgetsStore.budgets)

// 获取可用的月份选项
const monthOptions = computed(() => {
  const months = new Set()

  // 从预算数据中提取月份
  budgets.value.forEach(budget => {
    if (budget.start_date) {
      const month = getBudgetMonth(budget.start_date)
      months.add(month)
    }
  })

  // 转换为数组并排序，添加"全部"选项
  const monthArray = Array.from(months).sort()
  return [
    { text: '全部月份', value: '' },
    ...monthArray.map(month => ({ text: month, value: month }))
  ]
})

// 获取可用的类别选项
const categoryOptions = computed(() => {
  const categories = new Set()

  // 从预算数据中提取类别
  budgets.value.forEach(budget => {
    if (budget.category) {
      categories.add(budget.category)
    }
  })

  // 转换为数组并排序，添加"全部"选项
  const categoryArray = Array.from(categories).sort()
  return [
    { text: '全部类别', value: '' },
    ...categoryArray.map(category => ({ text: category, value: category }))
  ]
})

// 排序选项
const sortOptions = [
  { text: '默认排序', value: 'date_asc' },
  { text: '月份升序', value: 'date_asc' },
  { text: '月份降序', value: 'date_desc' },
  { text: '金额升序', value: 'amount_asc' },
  { text: '金额降序', value: 'amount_desc' },
  { text: '使用率升序', value: 'usage_asc' },
  { text: '使用率降序', value: 'usage_desc' }
]

// 排序和过滤后的预算列表
const sortedAndFilteredBudgets = computed(() => {
  let result = [...budgets.value]

  // 先过滤月份
  if (selectedMonth.value) {
    result = result.filter(budget => {
      const budgetMonth = getBudgetMonth(budget.start_date)
      return budgetMonth === selectedMonth.value
    })
  }

  // 再过滤类别
  if (selectedCategory.value) {
    result = result.filter(budget => {
      return budget.category === selectedCategory.value
    })
  }

  // 最后排序
  result.sort((a, b) => {
    switch (sortType.value) {
      case 'date_asc':
        return new Date(a.start_date) - new Date(b.start_date)
      case 'date_desc':
        return new Date(b.start_date) - new Date(a.start_date)
      case 'amount_asc':
        return (a.amount || 0) - (b.amount || 0)
      case 'amount_desc':
        return (b.amount || 0) - (a.amount || 0)
      case 'usage_asc':
        return (a.usage_percentage || 0) - (b.usage_percentage || 0)
      case 'usage_desc':
        return (b.usage_percentage || 0) - (a.usage_percentage || 0)
      default:
        return new Date(a.start_date) - new Date(b.start_date)
    }
  })

  return result
})

// 过滤后的警告预算（使用率 >= 80%）
const filteredWarningBudgets = computed(() => {
  return sortedAndFilteredBudgets.value.filter(budget => {
    const percentage = budget.usage_percentage || 0
    return percentage >= 80 && percentage < 100
  })
})

// 过滤后的超支预算（使用率 >= 100%）
const filteredExceededBudgets = computed(() => {
  return sortedAndFilteredBudgets.value.filter(budget => {
    const percentage = budget.usage_percentage || 0
    return percentage >= 100
  })
})

// 初始化
onMounted(async () => {
  // 从路由参数初始化筛选条件
  initializeFiltersFromRoute()

  await loadBudgets()
  await loadTransactions()
})

// 监听路由变化，更新筛选条件
watch(() => route.query, () => {
  initializeFiltersFromRoute()
}, { deep: true })

// 加载预算数据
const loadBudgets = async () => {
  loading.value = true
  try {
    await budgetsStore.fetchBudgets()
  } catch (error) {
    console.error('加载预算失败:', error)
    showToast({
      message: '加载预算失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 加载交易数据
const loadTransactions = async () => {
  try {
    await transactionsStore.fetchTransactions()
  } catch (error) {
    console.error('加载交易数据失败:', error)
  }
}

// 添加预算
const addBudget = () => {
  router.push('/budget/add')
}

// 查看预算详情
const viewBudget = (budget) => {
  router.push(`/budget/detail/${budget.id}`)
}

// 显示超支预算
const showExceededBudgets = () => {
  const categories = filteredExceededBudgets.value.map(b => b.category).join('、')
  showToast({
    message: `超支预算：${categories}`,
    type: 'fail',
    duration: 3000
  })
}

// 显示警告预算
const showWarningBudgets = () => {
  const categories = filteredWarningBudgets.value.map(b => b.category).join('、')
  showToast({
    message: `接近上限预算：${categories}`,
    type: 'warning',
    duration: 3000
  })
}

// 获取预算月份（格式：YYYY年MM月）
const getBudgetMonth = (startDate) => {
  if (!startDate) return ''
  const date = new Date(startDate)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  return `${year}年${String(month).padStart(2, '0')}月`
}

// 清除过滤器
const clearFilter = () => {
  selectedMonth.value = ''
  selectedCategory.value = ''
  sortType.value = 'date_asc'
}

// 获取空状态描述
const getEmptyDescription = () => {
  if (selectedMonth.value && selectedCategory.value) {
    return `${selectedMonth.value}的${selectedCategory.value}类别没有预算数据`
  } else if (selectedMonth.value) {
    return `${selectedMonth.value}没有预算数据`
  } else if (selectedCategory.value) {
    return `${selectedCategory.value}类别没有预算数据`
  } else {
    return '还没有设置预算'
  }
}

// 获取类别图标
const getCategoryIcon = (category) => {
  const icons = {
    '储蓄': '💰',
    '固定': '🏠',
    '流动': '🛒',
    '债务': '💳'
  }
  return icons[category] || '💰'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    warning: '警告',
    danger: '危险',
    exceeded: '超支'
  }
  return statusMap[status] || '正常'
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 100) return '#ee0a24'
  if (percentage >= 90) return '#ff976a'
  if (percentage >= 80) return '#ffd21e'
  return '#07c160'
}

// 格式化月份
const formatMonth = (startDate, endDate) => {
  if (!startDate) return ''
  const start = new Date(startDate)
  return `${start.getFullYear()}年${start.getMonth() + 1}月`
}

// 获取剩余天数
const getRemainingDays = (budget) => {
  if (!budget.end_date) return ''

  const now = new Date()
  const endDate = new Date(budget.end_date)
  const diffTime = endDate - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return '已过期'
  if (diffDays === 0) return '今天结束'
  if (diffDays <= 7) return `${diffDays}天后结束`
  return ''
}
</script>

<style scoped>
.budget-list-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 16px;
}

.budget-alerts {
  margin: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.budget-list {
  margin: 16px;
}

.empty-container {
  padding: 60px 20px;
  text-align: center;
}

.budget-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 16px;
}

.budget-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.budget-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.budget-card:active {
  transform: scale(0.98);
}

.budget-card.warning {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
}

.budget-card.danger {
  border-color: #ef4444;
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

.budget-card.exceeded {
  border-color: #dc2626;
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.budget-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.budget-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  background: #f0f9ff;
  border: 2px solid #e2e8f0;
}

.budget-icon.warning {
  background: #fef3c7;
  border-color: #f59e0b;
}

.budget-icon.danger {
  background: #fecaca;
  border-color: #ef4444;
}

.budget-icon.exceeded {
  background: #fecaca;
  border-color: #dc2626;
}

.budget-details {
  flex: 1;
}

.project-name {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.budget-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 14px;
  color: #969799;
}

.category-tag {
  background: #f2f3f5;
  color: #646566;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.period-text {
  font-size: 12px;
}

.status-indicator {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #f2f3f5;
  color: #646566;
}

.status-indicator.warning {
  background: #fff7cc;
  color: #ff976a;
}

.status-indicator.danger {
  background: #fff2e8;
  color: #ffd21e;
}

.status-indicator.exceeded {
  background: #fef0f0;
  color: #ee0a24;
}

.budget-progress {
  margin-top: 16px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.used-amount {
  font-size: 14px;
  font-weight: 600;
  color: #ef4444;
}

.total-amount {
  font-size: 14px;
  font-weight: 600;
  color: #3b82f6;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.progress-bar {
  flex: 1;
}

.progress-percentage {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  min-width: 35px;
  text-align: right;
}

.remaining-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.remaining-amount {
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.remaining-days {
  font-size: 11px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 8px;
}
</style>
