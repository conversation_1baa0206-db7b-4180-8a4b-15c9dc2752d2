#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
机器人配置文件
"""

import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class BotConfig:
    """机器人配置类"""

    # 基础配置
    DEBUG = os.getenv('BOT_DEBUG', 'True').lower() in ('true', '1', 't')

    # Web应用API配置
    WEB_API_BASE_URL = os.getenv('WEB_API_BASE_URL', 'http://localhost:8000/api')
    WEB_API_TOKEN = os.getenv('WEB_API_TOKEN', 'finance_app_token')

    # 机器人服务器配置
    BOT_SERVER_HOST = os.getenv('BOT_SERVER_HOST', '0.0.0.0')
    BOT_SERVER_PORT = int(os.getenv('BOT_SERVER_PORT', '8001'))

    # 预算检查配置
    BUDGET_CHECK_INTERVAL = int(os.getenv('BUDGET_CHECK_INTERVAL', '300'))  # 5分钟检查一次
    BUDGET_WARNING_THRESHOLD = float(os.getenv('BUDGET_WARNING_THRESHOLD', '0.8'))  # 80%预警
    BUDGET_DANGER_THRESHOLD = float(os.getenv('BUDGET_DANGER_THRESHOLD', '1.0'))   # 100%危险
    DISABLE_BUDGET_CHECK = os.getenv('DISABLE_BUDGET_CHECK', '0').lower() in ('1', 'true', 't', 'yes')

    # Telegram机器人配置
    TELEGRAM_ENABLED = os.getenv('TELEGRAM_ENABLED', 'False').lower() in ('true', '1', 't')
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
    TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', '')  # 可选，如果不配置会自动获取

    # 日志配置
    LOG_LEVEL = os.getenv('BOT_LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('BOT_LOG_FILE', 'logs/bot.log')

    # 支持的货币 - 只保留马币和美元
    SUPPORTED_CURRENCIES = ['MYR', 'USD']

    # 支持的交易类型
    TRANSACTION_TYPES = {
        '收入': 'income',
        '支出': 'expense',
        '转账': 'transfer'
    }



# 创建配置实例
config = BotConfig()
