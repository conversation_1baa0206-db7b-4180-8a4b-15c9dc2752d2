<template>
  <div class="transaction-detail-page">
    <van-nav-bar
      title="交易详情"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
    >
      <template #right>
        <van-icon name="edit" size="18" @click="editTransaction" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading size="24px" />
      <span>加载中...</span>
    </div>

    <!-- 主要内容 -->
    <div v-else-if="transaction" class="page-container">
      <!-- 交易金额卡片 -->
      <div class="amount-card" :class="transaction.type">
        <div class="amount-info">
          <div class="amount-label">
            {{ transaction.type === 'income' ? '收入金额' : '支出金额' }}
          </div>
          <div class="amount-value">
            {{ formatCurrency(transaction.amount, transaction.currency) }}
          </div>
          <div class="amount-type">
            <van-tag :type="transaction.type === 'income' ? 'success' : 'danger'" size="medium">
              {{ transaction.type === 'income' ? '收入' : '支出' }}
            </van-tag>
          </div>
        </div>
        <div class="amount-icon">
          {{ transaction.type === 'income' ? '💰' : '💸' }}
        </div>
      </div>

      <!-- 基本信息 -->
      <van-cell-group inset title="基本信息">
        <van-cell title="交易描述" :value="transaction.description" />
        <van-cell title="交易日期" :value="formatDate(transaction.date, 'YYYY-MM-DD')" />
        <van-cell title="交易分类" :value="transaction.category" />
        <van-cell title="交易账户" :value="getAccountName(transaction.account)" />
        <van-cell title="货币类型" :value="getCurrencyName(transaction.currency)" />
      </van-cell-group>

      <!-- 附加信息 -->
      <van-cell-group v-if="transaction.attachment" inset title="备注信息">
        <van-cell>
          <div class="attachment-content">
            {{ transaction.attachment }}
          </div>
        </van-cell>
      </van-cell-group>

      <!-- 时间信息 -->
      <van-cell-group inset title="时间信息">
        <van-cell title="创建时间" :value="formatDate(transaction.created_at, 'YYYY-MM-DD HH:mm')" />
        <van-cell title="更新时间" :value="formatDate(transaction.updated_at, 'YYYY-MM-DD HH:mm')" />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <van-button
          type="primary"
          size="large"
          @click="editTransaction"
          block
        >
          编辑交易
        </van-button>
        
        <van-button
          type="danger"
          size="large"
          plain
          @click="deleteTransaction"
          :loading="deleteLoading"
          block
        >
          删除交易
        </van-button>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <div class="error-icon">❌</div>
      <p>交易不存在或已被删除</p>
      <van-button type="primary" @click="$router.back()">
        返回
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useTransactionsStore } from '@/stores/transactions'
import { useAccountsStore } from '@/stores/accounts'
import { transactionsAPI } from '@/api/transactions'
import { showToast, showConfirmDialog } from 'vant'
import { formatCurrency, formatDate } from '@/utils/format'
import { getCurrencySymbol, getCurrencyName } from '@/config/currencies'

const router = useRouter()
const route = useRoute()
const transactionsStore = useTransactionsStore()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(true)
const deleteLoading = ref(false)
const transaction = ref(null)

// 初始化
onMounted(async () => {
  await loadData()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const transactionId = route.params.id
    
    // 并行加载账户数据和交易数据
    await Promise.all([
      accountsStore.fetchAccounts(),
      loadTransaction(transactionId)
    ])
    
  } catch (error) {
    console.error('加载数据失败:', error)
    showToast('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载交易数据
const loadTransaction = async (id) => {
  try {
    // 先从store中查找
    let transactionData = transactionsStore.transactions.find(t => t.id == id)
    
    if (!transactionData) {
      // 如果store中没有，从API获取
      const response = await transactionsAPI.getTransaction(id)
      transactionData = response.data || response
    }
    
    if (transactionData) {
      transaction.value = transactionData
    } else {
      showToast('交易不存在')
      router.back()
    }
  } catch (error) {
    console.error('加载交易失败:', error)
    showToast('加载交易失败')
    router.back()
  }
}

// 获取账户名称
const getAccountName = (accountId) => {
  const account = accountsStore.accounts.find(acc => acc.id === accountId)
  return account ? `${account.name} (${account.type})` : '未知账户'
}

// 货币相关函数已从统一配置导入

// 编辑交易
const editTransaction = () => {
  router.push(`/transaction/edit/${transaction.value.id}`)
}

// 删除交易
const deleteTransaction = async () => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这条交易记录吗？此操作不可撤销。'
    })

    deleteLoading.value = true

    await transactionsStore.deleteTransaction(transaction.value.id)

    showToast({
      message: '交易删除成功',
      type: 'success'
    })

    // 返回交易列表页面
    router.replace('/transactions')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除交易失败:', error)
      showToast({
        message: error.message || '删除交易失败',
        type: 'fail'
      })
    }
  } finally {
    deleteLoading.value = false
  }
}
</script>

<style scoped>
.transaction-detail-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container span {
  margin-top: 12px;
  color: #646566;
  font-size: 14px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-container p {
  color: #646566;
  font-size: 16px;
  margin: 0 0 24px 0;
}

.amount-card {
  background: white;
  margin: 16px;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.amount-card.income {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-left: 4px solid #07c160;
}

.amount-card.expense {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-left: 4px solid #ee0a24;
}

.amount-info {
  flex: 1;
}

.amount-label {
  font-size: 14px;
  color: #646566;
  margin-bottom: 8px;
}

.amount-value {
  font-size: 28px;
  font-weight: 700;
  color: #323233;
  margin-bottom: 8px;
}

.amount-icon {
  font-size: 36px;
  opacity: 0.8;
}

.attachment-content {
  line-height: 1.6;
  color: #323233;
  word-break: break-word;
}

.action-buttons {
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}

:deep(.van-cell__title) {
  font-weight: 500;
  color: #323233;
}

:deep(.van-cell__value) {
  color: #646566;
}
</style>
