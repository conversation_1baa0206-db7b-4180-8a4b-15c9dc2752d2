<template>
  <div class="edit-account-page">
    <van-nav-bar
      title="编辑账户"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
    >
      <template #right>
        <van-button
          type="primary"
          size="small"
          :loading="loading"
          @click="handleSubmit"
        >
          保存
        </van-button>
      </template>
    </van-nav-bar>

    <div class="page-container">
      <div v-if="accountLoading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>

      <van-form v-else @submit="handleSubmit" class="account-form">
        <!-- 基本信息 -->
        <van-cell-group inset title="基本信息">
          <van-field
            v-model="selectedCurrencyName"
            label="货币"
            placeholder="选择货币"
            readonly
            is-link
            @click="showCurrencyPicker = true"
            :rules="[{ required: true, message: '请选择货币' }]"
          />

          <van-field
            v-model="selectedTypeName"
            label="账户类型"
            placeholder="选择账户类型"
            readonly
            is-link
            @click="showTypePicker = true"
            :rules="[{ required: true, message: '请选择账户类型' }]"
          />

          <van-field
            v-model="selectedAccountName"
            label="账户名称"
            placeholder="选择账户名称"
            readonly
            is-link
            @click="showAccountNamePicker = true"
            :rules="[{ required: true, message: '请选择账户名称' }]"
          />

          <van-field
            v-model="form.initial_balance"
            label="初始余额"
            type="number"
            placeholder="0.00"
            :rules="[{ required: true, message: '请输入初始余额' }]"
          />
        </van-cell-group>

        <!-- 附加信息 -->
        <van-cell-group inset title="附加信息">
          <van-field
            v-model="form.description"
            label="描述"
            type="textarea"
            placeholder="添加账户描述（可选）"
            rows="3"
            autosize
            maxlength="200"
            show-word-limit
          />
        </van-cell-group>

        <!-- 危险操作 -->
        <van-cell-group inset title="危险操作">
          <van-cell
            title="删除账户"
            is-link
            @click="showDeleteConfirm = true"
            title-class="delete-text"
          >
            <template #icon>
              <van-icon name="delete-o" color="#ee0a24" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-form>
    </div>

    <!-- 账户类型选择器 -->
    <van-popup v-model:show="showTypePicker" position="bottom">
      <van-picker
        :columns="typeColumns"
        title="选择账户类型"
        @confirm="onTypeConfirm"
        @cancel="showTypePicker = false"
      />
    </van-popup>

    <!-- 货币选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom">
      <van-picker
        :columns="currencyColumns"
        title="选择货币"
        @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false"
      />
    </van-popup>

    <!-- 账户名称选择器 -->
    <van-popup v-model:show="showAccountNamePicker" position="bottom">
      <van-picker
        :columns="accountNameColumns"
        title="选择账户名称"
        @confirm="onAccountNameConfirm"
        @cancel="showAccountNamePicker = false"
      />
    </van-popup>

    <!-- 删除确认对话框 -->
    <van-dialog
      v-model:show="showDeleteConfirm"
      title="确认删除"
      message="删除账户后，相关交易记录将转移到默认账户。此操作不可撤销，确定要删除吗？"
      show-cancel-button
      @confirm="handleDelete"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAccountsStore } from '@/stores/accounts'
import { getCurrencyOptions } from '@/config/currencies'
import { showToast } from 'vant'

const router = useRouter()
const route = useRoute()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const accountLoading = ref(false)
const showTypePicker = ref(false)
const showCurrencyPicker = ref(false)
const showAccountNamePicker = ref(false)
const showDeleteConfirm = ref(false)

// 表单数据
const form = reactive({
  name: '',
  type: '',
  currency: 'MYR',
  initial_balance: '0',
  description: ''
})

// 账户类型数据
const accountTypes = [
  { text: '🏦 银行账户', value: 'bank' },
  { text: '💵 现金', value: 'cash' },
  { text: '💰 投资账户', value: 'investment' },
  { text: '🏪 电子钱包', value: 'ewallet' }
]

// 货币数据 - 使用统一配置
const currencies = getCurrencyOptions()

// 账户名称数据
const accountNames = {
  MYR: {
    bank: [
      { text: 'Maybank', value: 'Maybank' },
      { text: 'HLB', value: 'HLB' },
      { text: 'PBB', value: 'PBB' },
      { text: 'GXBank', value: 'GXBank' }
    ],
    ewallet: [
      { text: 'TNG eWallet', value: 'TNG eWallet' }
    ],
    investment: [
      { text: 'KDI', value: 'KDI' },
      { text: 'moomoo', value: 'moomoo' },
      { text: 'EPF', value: 'EPF' }
    ],
    cash: [
      { text: 'Cash', value: 'Cash' }
    ]
  },
  USD: {
    bank: [
      { text: 'ABA', value: 'ABA' }
    ],
    ewallet: [
      { text: 'Cash', value: 'Cash' }
    ],
    investment: [
      { text: 'Cash', value: 'Cash' }
    ],
    cash: [
      { text: 'Cash', value: 'Cash' }
    ]
  },
  CNY: {
    bank: [
      { text: 'Cash', value: 'Cash' }
    ],
    ewallet: [
      { text: 'Cash', value: 'Cash' }
    ],
    investment: [
      { text: 'Cash', value: 'Cash' }
    ],
    cash: [
      { text: 'Cash', value: 'Cash' }
    ]
  },
  EUR: {
    bank: [
      { text: 'Cash', value: 'Cash' }
    ],
    ewallet: [
      { text: 'Cash', value: 'Cash' }
    ],
    investment: [
      { text: 'Cash', value: 'Cash' }
    ],
    cash: [
      { text: 'Cash', value: 'Cash' }
    ]
  },
  GBP: {
    bank: [
      { text: 'Cash', value: 'Cash' }
    ],
    ewallet: [
      { text: 'Cash', value: 'Cash' }
    ],
    investment: [
      { text: 'Cash', value: 'Cash' }
    ],
    cash: [
      { text: 'Cash', value: 'Cash' }
    ]
  },
  JPY: {
    bank: [
      { text: 'Cash', value: 'Cash' }
    ],
    ewallet: [
      { text: 'Cash', value: 'Cash' }
    ],
    investment: [
      { text: 'Cash', value: 'Cash' }
    ],
    cash: [
      { text: 'Cash', value: 'Cash' }
    ]
  },
  SGD: {
    bank: [
      { text: 'Cash', value: 'Cash' }
    ],
    ewallet: [
      { text: 'Cash', value: 'Cash' }
    ],
    investment: [
      { text: 'Cash', value: 'Cash' }
    ],
    cash: [
      { text: 'Cash', value: 'Cash' }
    ]
  }
}

// 计算属性
const typeColumns = computed(() => accountTypes)
const currencyColumns = computed(() => currencies)

const accountNameColumns = computed(() => {
  if (!form.currency || !form.type) {
    return []
  }
  return accountNames[form.currency]?.[form.type] || []
})

const selectedTypeName = computed(() => {
  const type = accountTypes.find(t => t.value === form.type)
  return type ? type.text : ''
})

const selectedCurrencyName = computed(() => {
  const currency = currencies.find(c => c.value === form.currency)
  return currency ? currency.text : ''
})

const selectedAccountName = computed(() => {
  return form.name
})

// 获取账户ID
const accountId = computed(() => route.params.id)

// 初始化
onMounted(async () => {
  await loadAccount()
})

// 加载账户数据
const loadAccount = async () => {
  accountLoading.value = true
  try {
    // 先确保账户列表已加载
    if (accountsStore.accounts.length === 0) {
      await accountsStore.fetchAccounts()
    }

    // 从store中查找账户
    const account = accountsStore.accounts.find(acc => acc.id === accountId.value)

    if (account) {
      form.name = account.name
      form.type = account.type
      form.currency = account.currency
      form.initial_balance = account.initial_balance.toString()
      form.description = account.description || ''
    } else {
      showToast({
        message: '账户不存在',
        type: 'fail'
      })
      router.back()
    }
  } catch (error) {
    console.error('加载账户失败:', error)
    showToast({
      message: '加载账户失败',
      type: 'fail'
    })
  } finally {
    accountLoading.value = false
  }
}

// 方法
const onTypeConfirm = ({ selectedOptions }) => {
  const newType = selectedOptions[0]?.value || ''
  if (newType !== form.type) {
    form.type = newType
    form.name = '' // 重置账户名称
  }
  showTypePicker.value = false
}

const onCurrencyConfirm = ({ selectedOptions }) => {
  const newCurrency = selectedOptions[0]?.value || 'MYR'
  if (newCurrency !== form.currency) {
    form.currency = newCurrency
    form.type = '' // 重置账户类型
    form.name = '' // 重置账户名称
  }
  showCurrencyPicker.value = false
}

const onAccountNameConfirm = ({ selectedOptions }) => {
  form.name = selectedOptions[0]?.value || ''
  showAccountNamePicker.value = false
}

const validateForm = () => {
  if (!form.name.trim()) {
    showToast('请输入账户名称')
    return false
  }

  if (!form.type) {
    showToast('请选择账户类型')
    return false
  }

  if (!form.currency) {
    showToast('请选择货币')
    return false
  }

  if (form.initial_balance === '' || isNaN(parseFloat(form.initial_balance))) {
    showToast('请输入有效的初始余额')
    return false
  }

  return true
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const accountData = {
      name: form.name.trim(),
      type: form.type,
      currency: form.currency,
      initial_balance: parseFloat(form.initial_balance),
      description: form.description.trim()
    }

    await accountsStore.updateAccount(accountId.value, accountData)

    showToast({
      message: '账户更新成功',
      type: 'success'
    })

    // 返回上一页
    router.back()

  } catch (error) {
    console.error('更新账户失败:', error)
    showToast({
      message: error.message || '更新账户失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

const handleDelete = async () => {
  loading.value = true

  try {
    await accountsStore.deleteAccount(accountId.value)

    showToast({
      message: '账户删除成功',
      type: 'success'
    })

    // 返回账户列表页面
    router.replace('/accounts')

  } catch (error) {
    console.error('删除账户失败:', error)
    showToast({
      message: error.message || '删除账户失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
    showDeleteConfirm.value = false
  }
}
</script>

<style scoped>
.edit-account-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 12px;
}

.account-form {
  margin-bottom: 20px;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}

:deep(.van-field__label) {
  font-weight: 500;
  color: #323233;
}

:deep(.van-field__control) {
  color: #323233;
}

:deep(.van-field__control::placeholder) {
  color: #c8c9cc;
}

/* 删除按钮样式 */
:deep(.delete-text) {
  color: #ee0a24 !important;
}

/* 选择器样式 */
:deep(.van-popup) {
  border-radius: 16px 16px 0 0;
}

:deep(.van-picker__toolbar) {
  padding: 16px;
}

:deep(.van-picker__title) {
  font-size: 16px;
  font-weight: 600;
}

:deep(.van-picker__confirm) {
  color: #1989fa;
  font-weight: 600;
}

:deep(.van-picker__cancel) {
  color: #646566;
}

/* 对话框样式 */
:deep(.van-dialog__header) {
  padding: 24px 24px 12px;
}

:deep(.van-dialog__message) {
  padding: 12px 24px 24px;
  line-height: 1.6;
}

:deep(.van-dialog__footer) {
  padding: 12px 24px 24px;
}
</style>
