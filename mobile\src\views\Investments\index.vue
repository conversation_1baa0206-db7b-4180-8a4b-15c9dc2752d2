<template>
  <div class="investments-page">
    <van-nav-bar
      title="投资账号"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <van-button
          size="mini"
          type="primary"
          plain
          @click="showCurrencyPicker = true"
          class="currency-btn"
        >
          {{ selectedCurrency }}
        </van-button>
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 投资总资产卡片 -->
      <div class="investment-summary-card">
        <div class="summary-header">
          <div class="summary-title">
            <h3>投资总资产</h3>
            <div class="summary-year">2025年</div>
          </div>
          <div class="summary-icon">
            <van-icon name="chart-trending-o" size="24" />
          </div>
        </div>

        <div class="total-investment">
          <span class="currency">{{ getCurrencySymbol(selectedCurrency) }}</span>
          <span class="amount">{{ formatAmount(totalInvestmentAssets) }}</span>
        </div>

        <div class="investment-stats">
          <div class="stat-item">
            <span class="stat-label">总收益</span>
            <span class="stat-value positive">+{{ formatCurrency(totalProfit, selectedCurrency) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">总回报率</span>
            <span class="stat-value positive">+{{ totalReturnRate }}%</span>
          </div>
        </div>
      </div>

      <!-- 投资账户列表 -->
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>
      
      <div v-else-if="investmentAccounts.length === 0" class="empty-container">
        <div class="empty-icon">📈</div>
        <p>暂无投资账户</p>
        <van-button 
          type="primary" 
          size="small"
          @click="$router.push('/account/add')"
        >
          添加投资账户
        </van-button>
      </div>
      
      <van-cell-group v-else inset title="我的投资账户">
        <div 
          v-for="account in investmentAccounts" 
          :key="account.id"
          class="investment-account-card"
        >
          <div class="account-header">
            <div class="account-info">
              <div class="account-icon">📈</div>
              <div class="account-details">
                <h4>{{ account.name }}</h4>
                <span class="account-type">{{ formatAccountType(account.type) }}</span>
              </div>
            </div>
            <div class="account-balance">
              <span class="current-balance">{{ formatCurrency(account.current_balance || account.initial_balance, account.currency) }}</span>
            </div>
          </div>

          <div class="account-stats">
            <div class="stat-row">
              <div class="stat-item">
                <span class="stat-label">初始余额</span>
                <span class="stat-value">{{ formatCurrency(account.initial_balance, account.currency) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">当前余额</span>
                <span class="stat-value">{{ formatCurrency(account.current_balance || account.initial_balance, account.currency) }}</span>
              </div>
            </div>
            
            <div class="stat-row">
              <div class="stat-item">
                <span class="stat-label">入金数</span>
                <span class="stat-value">{{ formatCurrency(account.deposits || 0, account.currency) }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">出金数</span>
                <span class="stat-value">{{ formatCurrency(account.withdrawals || 0, account.currency) }}</span>
              </div>
            </div>
            
            <div class="stat-row">
              <div class="stat-item">
                <span class="stat-label">收益</span>
                <span class="stat-value" :class="{ positive: account.profit > 0, negative: account.profit < 0 }">
                  {{ account.profit > 0 ? '+' : '' }}{{ formatCurrency(account.profit || 0, account.currency) }}
                </span>
              </div>
              <div class="stat-item">
                <span class="stat-label">回报率</span>
                <span class="stat-value" :class="{ positive: account.return_rate > 0, negative: account.return_rate < 0 }">
                  {{ account.return_rate > 0 ? '+' : '' }}{{ account.return_rate || 0 }}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </van-cell-group>
    </div>

    <!-- 货币选择弹窗 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom" round>
      <div class="currency-picker">
        <div class="picker-header">
          <h3>选择货币</h3>
          <van-button size="mini" type="primary" @click="showCurrencyPicker = false">
            确定
          </van-button>
        </div>
        <van-cell-group>
          <van-cell
            v-for="currency in availableCurrencies"
            :key="currency.code"
            :title="currency.name"
            :label="currency.code"
            :value="currency.symbol"
            clickable
            @click="selectCurrency(currency.code)"
          >
            <template #right-icon>
              <van-icon
                v-if="selectedCurrency === currency.code"
                name="success"
                color="#1989fa"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAccountsStore } from '@/stores/accounts'
import { formatCurrency, getCurrencySymbol } from '@/utils/format'
import { getCurrencyOptionsForAccounts, DEFAULT_CURRENCY } from '@/config/currencies'
import { showToast } from 'vant'

const router = useRouter()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const showCurrencyPicker = ref(false)
const selectedCurrency = ref(localStorage.getItem('investments-currency') || DEFAULT_CURRENCY)

// 可用货币列表 - 使用统一配置
const availableCurrencies = ref(getCurrencyOptionsForAccounts())

// 计算属性
const investmentAccounts = computed(() => {
  return accountsStore.accounts
    .filter(account => account.type === 'investment' && account.currency === selectedCurrency.value)
    .map(account => ({
      ...account,
      // 模拟数据 - 实际应用中这些数据应该从API获取
      deposits: Math.floor(Math.random() * 10000) + 5000,
      withdrawals: Math.floor(Math.random() * 3000),
      profit: Math.floor(Math.random() * 2000) - 500,
      return_rate: (Math.random() * 20 - 5).toFixed(2)
    }))
})

const totalInvestmentAssets = computed(() => {
  return investmentAccounts.value.reduce((total, account) => {
    return total + (account.current_balance || account.initial_balance || 0)
  }, 0)
})

const totalProfit = computed(() => {
  return investmentAccounts.value.reduce((total, account) => {
    return total + (account.profit || 0)
  }, 0)
})

const totalReturnRate = computed(() => {
  if (investmentAccounts.value.length === 0) return '0.00'
  const avgReturn = investmentAccounts.value.reduce((total, account) => {
    return total + parseFloat(account.return_rate || 0)
  }, 0) / investmentAccounts.value.length
  return avgReturn.toFixed(2)
})

// 初始化
onMounted(async () => {
  await loadAccounts()
})

// 加载账户数据
const loadAccounts = async () => {
  loading.value = true
  try {
    await accountsStore.fetchAccountsWithBalances()
  } catch (error) {
    console.error('加载投资账户失败:', error)
    showToast({
      message: '加载投资账户失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 格式化账户类型
const formatAccountType = (type) => {
  const typeMap = {
    investment: '投资账户'
  }
  return typeMap[type] || type
}

// 选择货币
const selectCurrency = (currency) => {
  selectedCurrency.value = currency
  localStorage.setItem('investments-currency', currency)
  showCurrencyPicker.value = false
}

// 格式化金额（不带货币符号）
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>

<style scoped>
.investments-page {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  min-height: 100vh;
  padding-bottom: 20px;
}

.currency-btn {
  min-width: 50px;
  height: 28px;
  font-size: 12px;
  font-weight: 600;
}

.investment-summary-card {
  background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
  color: #1e293b;
  padding: 24px;
  margin: 16px;
  border-radius: 24px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(5, 150, 105, 0.15),
    0 8px 16px rgba(5, 150, 105, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.summary-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #059669;
  letter-spacing: 0.3px;
}

.summary-year {
  font-size: 12px;
  color: #6b7280;
  background: rgba(5, 150, 105, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  margin-top: 4px;
  width: fit-content;
}

.summary-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(5, 150, 105, 0.3);
}

.summary-icon .van-icon {
  color: white;
}

.total-investment {
  margin-bottom: 24px;
  display: flex;
  align-items: baseline;
  gap: 6px;
}

.currency {
  font-size: 20px;
  font-weight: 600;
  color: #64748b;
}

.amount {
  font-size: 36px;
  font-weight: 800;
  color: #0f172a;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  letter-spacing: -0.5px;
}

.investment-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
}

.stat-value.positive {
  color: #059669;
}

.stat-value.negative {
  color: #dc2626;
}

.investment-account-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.account-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.account-details h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.account-type {
  font-size: 12px;
  color: #6b7280;
}

.current-balance {
  font-size: 18px;
  font-weight: 700;
  color: #059669;
}

.account-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-row {
  display: flex;
  gap: 16px;
}

.stat-row .stat-item {
  flex: 1;
  text-align: left;
}

.stat-row .stat-label {
  font-size: 11px;
  color: #9ca3af;
  margin-bottom: 2px;
}

.stat-row .stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #969799;
  gap: 12px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.currency-picker {
  padding: 20px 0;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 16px;
  border-bottom: 1px solid #ebedf0;
}

.picker-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}
</style>
