<template>
  <div class="add-budget-page">
    <van-nav-bar
      title="添加预算"
      left-arrow
      @click-left="$router.back()"
      fixed
      placeholder
    />

    <div class="page-container">
      <van-form @submit="handleSubmit">
        <!-- 基本信息 -->
        <van-cell-group inset title="基本信息">
          <van-field
            v-model="form.project_name"
            name="project_name"
            label="项目名称"
            placeholder="请输入预算项目名称"
            :rules="[{ required: true, message: '请输入项目名称' }]"
          />

          <van-field
            v-model="form.category"
            name="category"
            label="预算类别"
            placeholder="选择支出类别"
            readonly
            is-link
            @click="showCategoryPicker = true"
            :rules="[{ required: true, message: '请选择预算类别' }]"
          />
          
          <van-field
            v-model="form.amount"
            name="amount"
            label="预算金额"
            placeholder="请输入预算金额"
            type="number"
            :rules="[
              { required: true, message: '请输入预算金额' },
              { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入有效的金额' }
            ]"
          >
            <template #left-icon>
              <span class="currency-symbol">{{ getCurrencySymbol(form.currency) }}</span>
            </template>
          </van-field>

          <van-field
            v-model="form.currency"
            name="currency"
            label="货币类型"
            placeholder="选择货币"
            readonly
            is-link
            @click="showCurrencyPicker = true"
            :rules="[{ required: true, message: '请选择货币类型' }]"
          />
        </van-cell-group>

        <!-- 预算周期 -->
        <van-cell-group inset title="预算周期">
          <van-field
            v-model="budgetPeriodDisplay"
            name="period"
            label="预算月份"
            placeholder="选择年月"
            readonly
            is-link
            @click="showDatePicker = true"
            :rules="[{ required: true, message: '请选择预算月份' }]"
          />
        </van-cell-group>

        <!-- 预算描述 -->
        <van-cell-group inset title="其他信息">
          <van-field
            v-model="form.description"
            name="description"
            label="预算描述"
            placeholder="添加预算说明（可选）"
            type="textarea"
            rows="3"
            maxlength="200"
            show-word-limit
          />
        </van-cell-group>

        <!-- 预算预览 -->
        <div class="budget-preview">
          <h3>预算预览</h3>
          <div class="preview-card">
            <div class="preview-header">
              <div class="category-info">
                <span class="category-icon">{{ getCategoryIcon(form.category) }}</span>
                <span class="category-name">{{ form.category || '未选择类别' }}</span>
              </div>
              <div class="amount">{{ formatCurrency(form.amount || 0) }}</div>
            </div>
            <div class="preview-details">
              <div class="detail-item">
                <span class="label">项目：</span>
                <span class="value">{{ form.project_name || '未填写' }}</span>
              </div>
              <div class="detail-item">
                <span class="label">时间：</span>
                <span class="value">{{ formatDateRange() }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-container">
          <van-button
            type="primary"
            native-type="submit"
            block
            round
            :loading="loading"
          >
            创建预算
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 类别选择器 -->
    <van-popup v-model:show="showCategoryPicker" position="bottom">
      <van-picker
        :columns="categoryColumns"
        @confirm="onCategoryConfirm"
        @cancel="showCategoryPicker = false"
      />
    </van-popup>

    <!-- 货币选择器 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom">
      <van-picker
        :columns="currencyColumns"
        @confirm="onCurrencyConfirm"
        @cancel="showCurrencyPicker = false"
      />
    </van-popup>

    <!-- 年月选择器 -->
    <van-popup v-model:show="showDatePicker" position="bottom">
      <van-picker
        :columns="[yearColumns, monthColumns]"
        :default-index="[yearColumns.findIndex(y => y.value === form.year), monthColumns.findIndex(m => m.value === form.month)]"
        title="选择年月"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useBudgetsStore } from '@/stores/budgets'
import { formatCurrency } from '@/utils/format'
import { showToast, showSuccessToast } from 'vant'
import { budgetCategories, getCategoryOptions } from '@/config/categories'
import { getCurrencyOptionsForSubscriptions, DEFAULT_CURRENCY } from '@/config/currencies'

const router = useRouter()
const budgetsStore = useBudgetsStore()

// 响应式数据
const loading = ref(false)
const showCategoryPicker = ref(false)
const showCurrencyPicker = ref(false)
const showDatePicker = ref(false)

// 表单数据
const form = ref({
  project_name: '',
  category: '',
  amount: '',
  currency: DEFAULT_CURRENCY, // 默认货币
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  description: ''
})





// 选择器选项 - 使用配置文件中的分类
const categoryColumns = getCategoryOptions('budget')

const currencyColumns = getCurrencyOptionsForSubscriptions()

// 年份选择器选项
const currentYear = new Date().getFullYear()
const yearColumns = []
for (let i = currentYear - 2; i <= currentYear + 5; i++) {
  yearColumns.push({ text: `${i}年`, value: i })
}

// 月份选择器选项
const monthColumns = [
  { text: '01月', value: 1 },
  { text: '02月', value: 2 },
  { text: '03月', value: 3 },
  { text: '04月', value: 4 },
  { text: '05月', value: 5 },
  { text: '06月', value: 6 },
  { text: '07月', value: 7 },
  { text: '08月', value: 8 },
  { text: '09月', value: 9 },
  { text: '10月', value: 10 },
  { text: '11月', value: 11 },
  { text: '12月', value: 12 }
]





// 计算属性
const budgetPeriodDisplay = computed(() => {
  if (!form.value.year || !form.value.month) {
    return '选择年月'
  }
  return `${form.value.year}年${String(form.value.month).padStart(2, '0')}月`
})

const formatDateRange = () => {
  if (!form.value.year || !form.value.month) {
    return '未选择时间'
  }
  return `${form.value.year}年${form.value.month}月`
}



// 选择器确认事件 - 修复：正确处理回调参数
const onCategoryConfirm = ({ selectedOptions }) => {
  form.value.category = selectedOptions[0].value
  showCategoryPicker.value = false
}

const onCurrencyConfirm = ({ selectedOptions }) => {
  form.value.currency = selectedOptions[0].value
  showCurrencyPicker.value = false
}

const onDateConfirm = ({ selectedOptions }) => {
  form.value.year = selectedOptions[0].value
  form.value.month = selectedOptions[1].value
  showDatePicker.value = false
}

const onStartDateConfirm = (result) => {
  // 修复：正确处理日期选择器回调参数
  console.log('开始日期选择器回调，接收到的值:', result, '类型:', typeof result)

  try {
    // 从回调结果中提取 selectedValues 数组
    const values = result.selectedValues || result
    console.log('提取的日期值:', values)

    if (Array.isArray(values) && values.length >= 3) {
      const year = parseInt(values[0])
      const month = parseInt(values[1]) - 1 // JavaScript月份从0开始
      const day = parseInt(values[2])

      console.log('解析的日期参数:', { year, month: month + 1, day })

      // 验证日期参数的有效性
      if (year && month >= 0 && month <= 11 && day >= 1 && day <= 31) {
        const date = new Date(year, month, day)
        console.log('创建的日期对象:', date)

        // 验证创建的日期是否有效
        if (!isNaN(date.getTime())) {
          // 修复：避免时区问题，直接格式化本地日期
          const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
          console.log('转换的日期字符串:', dateString)
          form.value.start_date = dateString
          startDate.value = values
        } else {
          console.error('创建的日期无效:', values)
          showToast({ message: '选择的日期无效', type: 'fail' })
        }
      } else {
        console.error('日期参数无效:', { year, month, day }, '原始值:', values)
        showToast({ message: '日期参数无效', type: 'fail' })
      }
    } else {
      console.error('日期格式错误，期望数组但收到:', values, '类型:', typeof values)
      showToast({ message: '日期格式错误', type: 'fail' })
    }
  } catch (error) {
    console.error('处理开始日期时出错:', error, '原始值:', result)
    showToast({ message: '处理日期时出错', type: 'fail' })
  }
  showStartDatePicker.value = false
}

const onEndDateConfirm = (result) => {
  // 修复：正确处理日期选择器回调参数
  console.log('结束日期选择器回调，接收到的值:', result, '类型:', typeof result)

  try {
    // 从回调结果中提取 selectedValues 数组
    const values = result.selectedValues || result
    console.log('提取的日期值:', values)

    if (Array.isArray(values) && values.length >= 3) {
      const year = parseInt(values[0])
      const month = parseInt(values[1]) - 1 // JavaScript月份从0开始
      const day = parseInt(values[2])

      console.log('解析的日期参数:', { year, month: month + 1, day })

      // 验证日期参数的有效性
      if (year && month >= 0 && month <= 11 && day >= 1 && day <= 31) {
        const date = new Date(year, month, day)
        console.log('创建的日期对象:', date)

        // 验证创建的日期是否有效
        if (!isNaN(date.getTime())) {
          // 修复：避免时区问题，直接格式化本地日期
          const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
          console.log('转换的日期字符串:', dateString)
          form.value.end_date = dateString
          endDate.value = values
        } else {
          console.error('创建的日期无效:', values)
          showToast({ message: '选择的日期无效', type: 'fail' })
        }
      } else {
        console.error('日期参数无效:', { year, month, day }, '原始值:', values)
        showToast({ message: '日期参数无效', type: 'fail' })
      }
    } else {
      console.error('日期格式错误，期望数组但收到:', values, '类型:', typeof values)
      showToast({ message: '日期格式错误', type: 'fail' })
    }
  } catch (error) {
    console.error('处理结束日期时出错:', error, '原始值:', result)
    showToast({ message: '处理日期时出错', type: 'fail' })
  }
  showEndDatePicker.value = false
}

// 获取类别图标
const getCategoryIcon = (category) => {
  const icons = {
    '储蓄': '💰',
    '固定': '🏠',
    '流动': '🛒',
    '债务': '💳'
  }
  return icons[category] || '💰'
}

// 获取货币符号
const getCurrencySymbol = (currency) => {
  const symbols = {
    'MYR': 'RM',
    'CNY': '¥',
    'USD': '$',
    'EUR': '€',
    'SGD': 'S$'
  }
  return symbols[currency] || 'RM'
}

// 格式化周期
const formatPeriod = (period) => {
  const periodMap = {
    monthly: '每月',
    weekly: '每周',
    custom: '自定义'
  }
  return periodMap[period] || period
}

// 提交表单
const handleSubmit = async () => {
  loading.value = true
  try {
    // 验证年月
    if (!form.value.year || !form.value.month) {
      showToast({
        message: '请选择预算年月',
        type: 'fail'
      })
      return
    }

    // 根据年月计算开始和结束日期
    const year = form.value.year
    const month = form.value.month

    // 使用字符串格式避免时区问题
    const startDate = `${year}-${String(month).padStart(2, '0')}-01`
    const lastDay = new Date(year, month, 0).getDate() // 获取该月的最后一天
    const endDate = `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`

    // 转换金额为数字并构建预算数据
    const budgetData = {
      ...form.value,
      amount: parseFloat(form.value.amount),
      period: 'monthly',
      start_date: startDate,
      end_date: endDate
    }

    await budgetsStore.createBudget(budgetData)

    showSuccessToast('预算创建成功')
    router.back()
  } catch (error) {
    console.error('创建预算失败:', error)
    showToast({
      message: error.message || '创建预算失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.add-budget-page {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

.currency-symbol {
  color: #969799;
  margin-right: 4px;
}

/* 预算预览 */
.budget-preview {
  margin: 16px;
}

.budget-preview h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.preview-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-icon {
  font-size: 20px;
}

.category-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

.amount {
  font-size: 20px;
  font-weight: 600;
  color: #1989fa;
}

.preview-details {
  border-top: 1px solid #ebedf0;
  padding-top: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 14px;
  color: #646566;
}

.value {
  font-size: 14px;
  color: #323233;
}

/* 提交按钮 */
.submit-container {
  margin: 24px 16px;
}

:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #323233;
}

:deep(.van-field__label) {
  color: #323233;
  font-weight: 500;
}

:deep(.van-field__control) {
  color: #323233;
}

:deep(.van-field__control::placeholder) {
  color: #c8c9cc;
}
</style>
