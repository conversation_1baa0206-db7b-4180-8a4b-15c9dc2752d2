<template>
  <div class="annual-budget-page">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="年度预算详情"
      left-arrow
      fixed
      placeholder
      safe-area-inset-top
      @click-left="$router.back()"
    />

    <!-- 页面内容 -->
    <div class="page-container">
      <!-- 筛选器 -->
      <div class="filter-section">
        <van-dropdown-menu>
          <van-dropdown-item
            v-model="selectedYear"
            :options="yearOptions"
            :title="selectedYearText"
          />
          <van-dropdown-item
            v-model="selectedCategory"
            :options="categoryOptions"
            :title="selectedCategoryText"
          />
        </van-dropdown-menu>
      </div>

      <!-- 年度统计概览 -->
      <div class="annual-stats-card">
        <div class="stats-header">
          <div class="stats-title">
            <div class="stats-icon">📊</div>
            <h3>{{ displayYearForTitle }}年{{ selectedCategory === 'all' ? '' : selectedCategory }}预算概览</h3>
          </div>
        </div>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">年度预算</div>
            <div class="stat-value">{{ formatCurrency(annualStats.totalBudget) }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">已使用</div>
            <div class="stat-value used">{{ formatCurrency(annualStats.totalUsed) }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">剩余</div>
            <div class="stat-value remaining">{{ formatCurrency(annualStats.totalRemaining) }}</div>
          </div>
          <div class="stat-item">
            <div class="stat-label">使用率</div>
            <div class="stat-value">{{ annualStats.usagePercentage }}%</div>
          </div>
        </div>
        <div class="annual-progress">
          <van-progress
            :percentage="Math.min(annualStats.usagePercentage, 100)"
            :color="getProgressColor(annualStats.usagePercentage)"
            :show-pivot="false"
            stroke-width="6"
          />
        </div>
      </div>

      <!-- 月度详情列表 -->
      <div class="monthly-details-card">
        <div class="details-header">
          <div class="details-title">
            <div class="details-icon">📋</div>
            <h3>月度预算详情</h3>
          </div>
        </div>
        <div class="monthly-list">
          <div
            v-for="month in monthlyDetails"
            :key="month.month"
            class="month-row"
            :class="month.status"
            @click="viewMonthDetail(month)"
          >
            <div class="month-info">
              <div class="month-name">{{ month.name }}</div>
              <div class="month-period">{{ month.period }}</div>
            </div>
            <div class="month-amounts">
              <div class="amount-item">
                <span class="amount-label">预算</span>
                <span class="amount-value">{{ formatCurrency(month.budget) }}</span>
              </div>
              <div class="amount-item">
                <span class="amount-label">已用</span>
                <span class="amount-value used">{{ formatCurrency(month.used) }}</span>
              </div>
              <div class="amount-item">
                <span class="amount-label">剩余</span>
                <span class="amount-value" :class="{ 'remaining': month.remaining >= 0, 'over-budget': month.remaining < 0 }">
                  {{ formatCurrency(month.remaining) }}
                </span>
              </div>
            </div>
            <div class="month-status">
              <div class="usage-percentage">{{ month.usagePercentage }}%</div>
              <div class="status-indicator" :class="month.status">
                {{ getStatusText(month.status) }}
              </div>
            </div>
            <van-icon name="arrow" class="arrow-icon" />
          </div>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <van-back-top
      v-show="showBackTop"
      :offset="100"
      :teleport="false"
      @click="scrollToTop"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useBudgetsStore } from '@/stores/budgets'
import { useTransactionsStore } from '@/stores/transactions'

const router = useRouter()
const budgetsStore = useBudgetsStore()
const transactionsStore = useTransactionsStore()

// 响应式数据
const selectedYear = ref('all')
const selectedCategory = ref('all')
const showBackTop = ref(false)

// 年份选项（当前年份前后各2年）
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const options = [{ text: '全部年份', value: 'all' }]
  for (let i = currentYear - 2; i <= currentYear + 2; i++) {
    options.push({ text: `${i}年`, value: i })
  }
  return options
})

// 类别选项
const categoryOptions = [
  { text: '全部类别', value: 'all' },
  { text: '储蓄', value: '储蓄' },
  { text: '固定', value: '固定' },
  { text: '流动', value: '流动' },
  { text: '债务', value: '债务' }
]

// 计算显示的年份文本
const selectedYearText = computed(() => {
  if (selectedYear.value === 'all') {
    return '全部年份'
  }
  return `${selectedYear.value}年`
})

// 计算显示的类别文本
const selectedCategoryText = computed(() => {
  if (selectedCategory.value === 'all') {
    return '全部类别'
  }
  return selectedCategory.value
})

// 计算标题显示的年份
const displayYearForTitle = computed(() => {
  return selectedYear.value === 'all' ? new Date().getFullYear() : selectedYear.value
})

// 年度统计数据
const annualStats = computed(() => {
  let totalBudget = 0
  let totalUsed = 0

  // 处理年份选择
  const years = selectedYear.value === 'all'
    ? [new Date().getFullYear()] // 如果选择全部年份，默认显示当前年份
    : [selectedYear.value]

  years.forEach(year => {
    for (let month = 1; month <= 12; month++) {
      const monthData = calculateMonthlyBudget(year, month, selectedCategory.value)
      totalBudget += monthData.budget
      totalUsed += monthData.used
    }
  })

  const totalRemaining = totalBudget - totalUsed
  const usagePercentage = totalBudget > 0 ? Math.round((totalUsed / totalBudget) * 100) : 0

  return {
    totalBudget,
    totalUsed,
    totalRemaining,
    usagePercentage
  }
})

// 月度详情数据
const monthlyDetails = computed(() => {
  const months = []
  const monthNames = [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ]

  // 处理年份选择
  const displayYear = selectedYear.value === 'all'
    ? new Date().getFullYear() // 如果选择全部年份，默认显示当前年份
    : selectedYear.value

  for (let month = 1; month <= 12; month++) {
    const monthData = calculateMonthlyBudget(displayYear, month, selectedCategory.value)
    months.push({
      month,
      name: monthNames[month - 1],
      period: `${displayYear}年${String(month).padStart(2, '0')}月`,
      ...monthData
    })
  }

  // 按月份从小到大排序（用户偏好）
  return months.sort((a, b) => a.month - b.month)
})

// 格式化货币
const formatCurrency = (amount) => {
  return `RM ${amount.toFixed(2)}`
}

// 获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 100) return '#ee0a24'
  if (percentage >= 90) return '#ff976a'
  if (percentage >= 80) return '#ffd21e'
  return '#07c160'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    warning: '警告',
    danger: '危险',
    exceeded: '超支'
  }
  return statusMap[status] || '正常'
}

// 查看月度详情
const viewMonthDetail = (month) => {
  // 构建路由参数，保持当前的筛选条件
  const year = selectedYear.value === 'all' ? new Date().getFullYear() : selectedYear.value
  const category = selectedCategory.value === 'all' ? 'all' : selectedCategory.value

  // 跳转到预算列表页面，并传递筛选参数
  router.push({
    name: 'BudgetsList',
    query: {
      year: year.toString(),
      month: month.month.toString(),
      category: category !== 'all' ? category : undefined, // 只有非全部类别时才传递
      sort: 'date_asc' // 默认按日期排序
    }
  })
}

// 计算指定月份的预算情况
const calculateMonthlyBudget = (year, month, category = 'all') => {
  const monthStr = String(month).padStart(2, '0')
  const startDate = `${year}-${monthStr}-01`

  // 计算月末日期
  const nextMonth = month === 12 ? 1 : month + 1
  const nextYear = month === 12 ? year + 1 : year
  const endDate = new Date(nextYear, nextMonth - 1, 0).toISOString().split('T')[0]

  // 获取该月的预算总额和使用情况
  let monthlyBudget = 0
  let monthlyUsed = 0

  if (budgetsStore.budgets) {
    budgetsStore.budgets.forEach(budget => {
      // 检查预算期间是否与指定月份重叠
      if (budget.start_date <= endDate && budget.end_date >= startDate) {
        // 如果选择了特定类别，只计算该类别的预算
        if (category === 'all' || budget.category === category) {
          monthlyBudget += budget.amount || 0
          monthlyUsed += budget.used_amount || 0
        }
      }
    })
  }

  const remaining = monthlyBudget - monthlyUsed
  const usagePercentage = monthlyBudget > 0 ? Math.round((monthlyUsed / monthlyBudget) * 100) : 0

  // 确定状态
  let status = 'normal'
  if (usagePercentage >= 100) {
    status = 'exceeded'
  } else if (usagePercentage >= 90) {
    status = 'danger'
  } else if (usagePercentage >= 80) {
    status = 'warning'
  }

  return {
    budget: monthlyBudget,
    used: monthlyUsed,
    remaining,
    usagePercentage,
    status
  }
}

// 返回顶部功能
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 监听滚动事件
const handleScroll = () => {
  showBackTop.value = window.scrollY > 300
}

// 页面加载时获取数据
onMounted(async () => {
  await budgetsStore.fetchBudgets()
  await transactionsStore.fetchTransactions()

  // 添加滚动监听
  window.addEventListener('scroll', handleScroll)
})

// 页面卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.annual-budget-page {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  min-height: 100vh;
}

.page-container {
  padding-bottom: 20px;
}

/* 筛选器样式 */
.filter-section {
  margin: 16px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 12px 24px rgba(30, 58, 138, 0.12);
}

/* 自定义下拉菜单样式 */
.filter-section :deep(.van-dropdown-menu) {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 20px;
}

.filter-section :deep(.van-dropdown-menu__bar) {
  background: transparent;
  box-shadow: none;
  border-radius: 20px;
}

.filter-section :deep(.van-dropdown-menu__item) {
  padding: 16px 20px;
  font-weight: 600;
  color: #1e293b;
  border-right: 1px solid rgba(30, 58, 138, 0.1);
}

.filter-section :deep(.van-dropdown-menu__item:last-child) {
  border-right: none;
}

.filter-section :deep(.van-dropdown-menu__title) {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
}

.filter-section :deep(.van-dropdown-menu__title--active) {
  color: #1e40af;
}

.filter-section :deep(.van-dropdown-item) {
  border-radius: 0 0 20px 20px;
  overflow: hidden;
}

/* 年度统计卡片 */
.annual-stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  margin: 0 16px 16px;
  border-radius: 20px;
  padding: 18px;
  box-shadow: 0 12px 24px rgba(30, 58, 138, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.stats-header {
  margin-bottom: 16px;
}

.stats-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stats-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.stats-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.25);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 12px;
  text-align: center;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
}

.stat-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 14px;
  font-weight: 700;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.stat-value.used {
  color: #dc2626;
}

.stat-value.remaining {
  color: #059669;
}

.annual-progress {
  margin-top: 8px;
}

/* 月度详情卡片 */
.monthly-details-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  margin: 0 16px 16px;
  border-radius: 20px;
  padding: 18px;
  box-shadow: 0 12px 24px rgba(30, 58, 138, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.details-header {
  margin-bottom: 16px;
}

.details-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.details-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.details-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.25);
}

.monthly-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.month-row {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  padding: 16px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
}

.month-row:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.month-row.warning {
  border-color: rgba(245, 158, 11, 0.3);
  background: rgba(255, 251, 235, 0.8);
}

.month-row.danger {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(254, 242, 242, 0.8);
}

.month-row.exceeded {
  border-color: rgba(220, 38, 38, 0.3);
  background: rgba(254, 242, 242, 0.8);
}

.month-info {
  min-width: 60px;
}

.month-name {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 2px;
}

.month-period {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.month-amounts {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.amount-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 600;
  min-width: 30px;
}

.amount-value {
  font-size: 11px;
  font-weight: 600;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.amount-value.used {
  color: #dc2626;
}

.amount-value.remaining {
  color: #059669;
}

.amount-value.over-budget {
  color: #dc2626;
}

.month-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 50px;
}

.usage-percentage {
  font-size: 12px;
  font-weight: 700;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.status-indicator {
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: 600;
  text-align: center;
}

.status-indicator.normal {
  background: #d1fae5;
  color: #065f46;
}

.status-indicator.warning {
  background: #fef3c7;
  color: #92400e;
}

.status-indicator.danger {
  background: #fecaca;
  color: #991b1b;
}

.status-indicator.exceeded {
  background: #fecaca;
  color: #991b1b;
}

.arrow-icon {
  color: #64748b;
  font-size: 14px;
}

/* 返回顶部按钮样式 */
.annual-budget-page :deep(.van-back-top) {
  right: 20px;
  bottom: 80px;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  border-radius: 50%;
  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);
  transition: all 0.3s ease;
}

.annual-budget-page :deep(.van-back-top:active) {
  transform: scale(0.95);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.4);
}

.annual-budget-page :deep(.van-back-top .van-icon) {
  color: white;
  font-size: 20px;
  font-weight: bold;
}
</style>
