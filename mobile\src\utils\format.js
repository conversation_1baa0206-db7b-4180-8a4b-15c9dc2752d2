/**
 * 格式化工具函数
 */

import { getCurrencySymbol as getCurrencySymbolFromConfig } from '@/config/currencies'

// 货币符号映射 - 使用统一配置
export const getCurrencySymbol = getCurrencySymbolFromConfig

// 货币格式化
export const formatCurrency = (amount, currency = 'MYR', decimals = 2) => {
  if (amount === null || amount === undefined || isNaN(amount)) {
    const symbol = getCurrencySymbol(currency)
    return `${symbol} 0.00`
  }

  const num = Number(amount)
  const symbol = getCurrencySymbol(currency)
  return `${symbol} ${num.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}

// 数字格式化（添加千分位分隔符）
export const formatNumber = (num, decimals = 2) => {
  if (num === null || num === undefined || isNaN(num)) {
    return '0'
  }
  
  const number = Number(num)
  return number.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 百分比格式化
export const formatPercent = (value, decimals = 1) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0%'
  }
  
  const num = Number(value)
  return `${num.toFixed(decimals)}%`
}

// 日期格式化
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'YYYY/MM/DD':
      return `${year}/${month}/${day}`
    case 'MM-DD':
      return `${month}-${day}`
    case 'YYYY-MM-DD HH:mm':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    case 'YYYY-MM-DD HH:mm:ss':
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    case 'HH:mm':
      return `${hours}:${minutes}`
    default:
      return `${year}-${month}-${day}`
  }
}

// 相对时间格式化
export const formatRelativeTime = (date) => {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (seconds < 60) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return formatDate(date)
  }
}

// 文件大小格式化
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// 手机号格式化
export const formatPhone = (phone) => {
  if (!phone) return ''
  
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
  
  return phone
}

// 银行卡号格式化
export const formatBankCard = (cardNumber) => {
  if (!cardNumber) return ''
  
  const cleaned = cardNumber.replace(/\D/g, '')
  return cleaned.replace(/(\d{4})(?=\d)/g, '$1 ')
}

// 交易类型格式化
export const formatTransactionType = (type) => {
  const typeMap = {
    income: '收入',
    expense: '支出',
    transfer: '转账'
  }
  
  return typeMap[type] || type
}

// 账户类型格式化
export const formatAccountType = (type) => {
  const typeMap = {
    bank: '银行账户',
    cash: '现金',
    credit: '信用卡',
    investment: '投资账户',
    loan: '贷款账户'
  }
  
  return typeMap[type] || type
}

// 预算周期格式化
export const formatBudgetPeriod = (period) => {
  const periodMap = {
    monthly: '月度',
    quarterly: '季度',
    yearly: '年度',
    weekly: '周度'
  }
  
  return periodMap[period] || period
}

// 截断文本
export const truncateText = (text, maxLength = 20) => {
  if (!text) return ''
  
  if (text.length <= maxLength) {
    return text
  }
  
  return text.substring(0, maxLength) + '...'
}

// 高亮搜索关键词
export const highlightKeyword = (text, keyword) => {
  if (!text || !keyword) return text
  
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 验证邮箱格式
export const isValidEmail = (email) => {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return regex.test(email)
}

// 验证手机号格式
export const isValidPhone = (phone) => {
  const regex = /^1[3-9]\d{9}$/
  return regex.test(phone)
}

// 生成随机颜色
export const generateRandomColor = () => {
  const colors = [
    '#1989fa', '#07c160', '#ff976a', '#ee0a24', '#9c26b0',
    '#ff9800', '#4caf50', '#2196f3', '#e91e63', '#795548'
  ]
  
  return colors[Math.floor(Math.random() * colors.length)]
}

// 获取文件扩展名
export const getFileExtension = (filename) => {
  if (!filename) return ''
  
  const lastDot = filename.lastIndexOf('.')
  if (lastDot === -1) return ''
  
  return filename.substring(lastDot + 1).toLowerCase()
}
