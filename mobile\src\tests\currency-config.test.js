/**
 * 货币配置测试
 * 验证货币配置的正确性和一致性
 */

import { describe, it, expect } from 'vitest'
import {
  supportedCurrencies,
  currencySymbols,
  getCurrencySymbol,
  getCurrencyName,
  getCurrencyOptions,
  getCurrencyOptionsForAccounts,
  getCurrencyOptionsForSubscriptions,
  DEFAULT_CURRENCY,
  isValidCurrency
} from '@/config/currencies'

describe('货币配置测试', () => {
  it('应该只包含MYR和USD两种货币', () => {
    expect(supportedCurrencies).toHaveLength(2)
    expect(supportedCurrencies.map(c => c.code)).toEqual(['MYR', 'USD'])
  })

  it('货币符号映射应该正确', () => {
    expect(currencySymbols.MYR).toBe('RM')
    expect(currencySymbols.USD).toBe('$')
    expect(Object.keys(currencySymbols)).toHaveLength(2)
  })

  it('getCurrencySymbol函数应该返回正确的符号', () => {
    expect(getCurrencySymbol('MYR')).toBe('RM')
    expect(getCurrencySymbol('USD')).toBe('$')
    expect(getCurrencySymbol('INVALID')).toBe('INVALID')
  })

  it('getCurrencyName函数应该返回正确的名称', () => {
    expect(getCurrencyName('MYR')).toBe('MYR - 马来西亚林吉特')
    expect(getCurrencyName('USD')).toBe('USD - 美元')
    expect(getCurrencyName('INVALID')).toBe('INVALID')
  })

  it('getCurrencyOptions应该返回正确格式的选项', () => {
    const options = getCurrencyOptions()
    expect(options).toHaveLength(2)
    expect(options[0]).toEqual({
      text: 'MYR - 马来西亚林吉特',
      value: 'MYR'
    })
    expect(options[1]).toEqual({
      text: 'USD - 美元',
      value: 'USD'
    })
  })

  it('getCurrencyOptionsForAccounts应该返回正确格式的选项', () => {
    const options = getCurrencyOptionsForAccounts()
    expect(options).toHaveLength(2)
    expect(options[0]).toEqual({
      code: 'MYR',
      name: '马来西亚林吉特',
      symbol: 'RM'
    })
    expect(options[1]).toEqual({
      code: 'USD',
      name: '美元',
      symbol: '$'
    })
  })

  it('getCurrencyOptionsForSubscriptions应该返回正确格式的选项', () => {
    const options = getCurrencyOptionsForSubscriptions()
    expect(options).toHaveLength(2)
    expect(options[0]).toEqual({
      text: '马来西亚林吉特 (MYR)',
      value: 'MYR'
    })
    expect(options[1]).toEqual({
      text: '美元 (USD)',
      value: 'USD'
    })
  })

  it('默认货币应该是MYR', () => {
    expect(DEFAULT_CURRENCY).toBe('MYR')
  })

  it('isValidCurrency应该正确验证货币代码', () => {
    expect(isValidCurrency('MYR')).toBe(true)
    expect(isValidCurrency('USD')).toBe(true)
    expect(isValidCurrency('CNY')).toBe(false)
    expect(isValidCurrency('EUR')).toBe(false)
    expect(isValidCurrency('INVALID')).toBe(false)
  })

  it('不应该包含已移除的货币', () => {
    const removedCurrencies = ['CNY', 'EUR', 'GBP', 'JPY', 'SGD', 'KRW']
    const supportedCodes = supportedCurrencies.map(c => c.code)
    
    removedCurrencies.forEach(currency => {
      expect(supportedCodes).not.toContain(currency)
      expect(isValidCurrency(currency)).toBe(false)
    })
  })
})

describe('货币-银行账户级联选择逻辑测试', () => {
  it('应该支持根据货币筛选账户的逻辑', () => {
    // 模拟账户数据
    const mockAccounts = [
      { id: '1', name: '马来亚银行', currency: 'MYR', type: 'bank' },
      { id: '2', name: '美国银行', currency: 'USD', type: 'bank' },
      { id: '3', name: '现金', currency: 'MYR', type: 'cash' },
      { id: '4', name: '投资账户', currency: 'USD', type: 'investment' }
    ]

    // 测试按货币筛选
    const myrAccounts = mockAccounts.filter(acc => acc.currency === 'MYR')
    const usdAccounts = mockAccounts.filter(acc => acc.currency === 'USD')

    expect(myrAccounts).toHaveLength(2)
    expect(usdAccounts).toHaveLength(2)
    expect(myrAccounts.every(acc => acc.currency === 'MYR')).toBe(true)
    expect(usdAccounts.every(acc => acc.currency === 'USD')).toBe(true)
  })

  it('应该验证KDI出金账户只支持MYR', () => {
    // 模拟KDI出金账户筛选逻辑
    const mockAccounts = [
      { id: '1', name: '马来亚银行', currency: 'MYR', type: 'bank' },
      { id: '2', name: '美国银行', currency: 'USD', type: 'bank' },
      { id: '3', name: 'Touch n Go', currency: 'MYR', type: 'ewallet' },
      { id: '4', name: 'PayPal', currency: 'USD', type: 'ewallet' }
    ]

    // KDI出金账户筛选条件：只显示马币的银行账户和电子钱包
    const kdiWithdrawAccounts = mockAccounts.filter(account =>
      account.currency === 'MYR' &&
      (account.type === 'bank' || account.type === 'ewallet')
    )

    expect(kdiWithdrawAccounts).toHaveLength(2)
    expect(kdiWithdrawAccounts.every(acc => acc.currency === 'MYR')).toBe(true)
    expect(kdiWithdrawAccounts.every(acc => 
      acc.type === 'bank' || acc.type === 'ewallet'
    )).toBe(true)
  })
})
