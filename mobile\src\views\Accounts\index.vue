<template>
  <div class="accounts-page">
    <van-nav-bar
      title="账户管理"
      fixed
      placeholder
      safe-area-inset-top
    >
      <template #right>
        <div class="nav-actions">
          <van-button
            size="mini"
            type="primary"
            plain
            @click="showCurrencyPicker = true"
            class="currency-btn"
          >
            {{ selectedCurrency }}
          </van-button>
          <van-icon name="plus" size="18" @click="$router.push('/account/add')" />
        </div>
      </template>
    </van-nav-bar>

    <div class="page-container">
      <!-- 总资产卡片 -->
      <div class="total-assets-card">
        <div class="assets-header">
          <div class="assets-title">
            <h3>总资产</h3>
            <div class="assets-trend">
              <van-icon name="arrow-up" size="12" />
              <span>+2.5%</span>
            </div>
          </div>
          <div class="assets-icon">
            <van-icon name="gold-coin-o" size="24" />
          </div>
        </div>

        <div class="total-amount">
          <span class="currency">{{ getCurrencySymbol(selectedCurrency) }}</span>
          <span class="amount">{{ formatAmount(filteredTotalAssets) }}</span>
        </div>

        <div class="assets-breakdown-container">
          <div class="assets-breakdown" ref="breakdownRef">
            <div class="breakdown-item">
              <div class="breakdown-icon cash">💵</div>
              <div class="breakdown-info">
                <span class="breakdown-label">现金</span>
                <span class="breakdown-value">{{ formatCurrency(filteredCashAssets, selectedCurrency) }}</span>
              </div>
            </div>
            <div class="breakdown-item">
              <div class="breakdown-icon bank">🏦</div>
              <div class="breakdown-info">
                <span class="breakdown-label">银行</span>
                <span class="breakdown-value">{{ formatCurrency(filteredBankAssets, selectedCurrency) }}</span>
              </div>
            </div>
            <div class="breakdown-item clickable" @click="$router.push('/investments')">
              <div class="breakdown-icon investment">📈</div>
              <div class="breakdown-info">
                <span class="breakdown-label">投资</span>
                <span class="breakdown-value">{{ formatCurrency(filteredInvestmentAssets, selectedCurrency) }}</span>
              </div>
            </div>
            <div class="breakdown-item">
              <div class="breakdown-icon ewallet">🏪</div>
              <div class="breakdown-info">
                <span class="breakdown-label">电子钱包</span>
                <span class="breakdown-value">{{ formatCurrency(filteredEwalletAssets, selectedCurrency) }}</span>
              </div>
            </div>
          </div>

          <!-- 滑动指示器 -->
          <div class="breakdown-indicators" v-if="showIndicators">
            <div
              v-for="(indicator, index) in indicators"
              :key="index"
              class="indicator"
              :class="{ active: currentIndicator === index }"
            ></div>
          </div>
        </div>

        <div class="assets-actions">
          <van-button
            size="small"
            type="primary"
            plain
            @click="$router.push('/account/add')"
          >
            添加账户
          </van-button>
          <van-button
            size="small"
            type="primary"
            plain
            @click="$router.push('/investments')"
          >
            投资账号
          </van-button>
          <van-button
            size="small"
            type="primary"
            plain
            @click="$router.push('/statistics')"
          >
            查看统计
          </van-button>
        </div>
      </div>

      <!-- 账户列表 -->
      <div v-if="loading" class="loading-container">
        <van-loading size="24px" />
        <span>加载中...</span>
      </div>
      
      <div v-else-if="accounts.length === 0" class="empty-container">
        <div class="empty-icon">🏦</div>
        <p>暂无账户</p>
        <van-button 
          type="primary" 
          size="small"
          @click="$router.push('/account/add')"
        >
          添加账户
        </van-button>
      </div>
      
      <van-cell-group v-else inset title="我的账户">
        <van-cell
          v-for="account in accounts"
          :key="account.id"
          :title="account.name"
          :label="formatAccountType(account.type)"
          :value="formatCurrency(account.current_balance || account.initial_balance, account.currency)"
          is-link
          @click="viewAccount(account)"
        >
          <template #icon>
            <div class="account-icon" :class="account.type">
              {{ getAccountIcon(account.type) }}
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 浮动添加按钮 -->
    <van-floating-bubble
      axis="xy"
      icon="plus"
      :gap="{ x: 24, y: 80 }"
      @click="$router.push('/account/add')"
    />

    <!-- 货币选择弹窗 -->
    <van-popup v-model:show="showCurrencyPicker" position="bottom" round>
      <div class="currency-picker">
        <div class="picker-header">
          <h3>选择货币</h3>
          <van-button size="mini" type="primary" @click="showCurrencyPicker = false">
            确定
          </van-button>
        </div>
        <van-cell-group>
          <van-cell
            v-for="currency in availableCurrencies"
            :key="currency.code"
            :title="currency.name"
            :label="currency.code"
            :value="currency.symbol"
            clickable
            @click="selectCurrency(currency.code)"
          >
            <template #right-icon>
              <van-icon
                v-if="selectedCurrency === currency.code"
                name="success"
                color="#1989fa"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAccountsStore } from '@/stores/accounts'
import { formatCurrency, getCurrencySymbol } from '@/utils/format'
import { getCurrencyOptionsForAccounts } from '@/config/currencies'
import { showToast } from 'vant'

const router = useRouter()
const accountsStore = useAccountsStore()

// 响应式数据
const loading = ref(false)
const showCurrencyPicker = ref(false)
const selectedCurrency = ref(localStorage.getItem('accounts-currency') || 'MYR')
const breakdownRef = ref(null)
const showIndicators = ref(false)
const currentIndicator = ref(0)
const indicators = ref([0, 1]) // 两页指示器

// 可用货币列表 - 使用统一配置
const availableCurrencies = ref(getCurrencyOptionsForAccounts())

// 计算属性
const accounts = computed(() => accountsStore.accounts)

// 按货币过滤的资产
const filteredAccounts = computed(() =>
  accountsStore.accounts.filter(account => account.currency === selectedCurrency.value)
)

const filteredTotalAssets = computed(() => {
  return filteredAccounts.value.reduce((total, account) => {
    return total + (account.current_balance || account.initial_balance || 0)
  }, 0)
})

const filteredCashAssets = computed(() => {
  return filteredAccounts.value
    .filter(account => account.type === 'cash')
    .reduce((total, account) => {
      return total + (account.current_balance || account.initial_balance || 0)
    }, 0)
})

const filteredBankAssets = computed(() => {
  return filteredAccounts.value
    .filter(account => account.type === 'bank')
    .reduce((total, account) => {
      return total + (account.current_balance || account.initial_balance || 0)
    }, 0)
})

const filteredInvestmentAssets = computed(() => {
  return filteredAccounts.value
    .filter(account => account.type === 'investment')
    .reduce((total, account) => {
      return total + (account.current_balance || account.initial_balance || 0)
    }, 0)
})

const filteredEwalletAssets = computed(() => {
  return filteredAccounts.value
    .filter(account => account.type === 'ewallet')
    .reduce((total, account) => {
      return total + (account.current_balance || account.initial_balance || 0)
    }, 0)
})

// 初始化
onMounted(async () => {
  await loadAccounts()
  await nextTick()
  setupBreakdownScroll()
})

// 设置资产分类滑动
const setupBreakdownScroll = () => {
  if (!breakdownRef.value) return

  const container = breakdownRef.value
  const containerWidth = container.offsetWidth
  const scrollWidth = container.scrollWidth

  // 如果内容超出容器宽度，显示指示器
  showIndicators.value = scrollWidth > containerWidth

  if (showIndicators.value) {
    // 监听滚动事件
    container.addEventListener('scroll', handleBreakdownScroll)
  }
}

// 处理滑动指示器
const handleBreakdownScroll = () => {
  if (!breakdownRef.value) return

  const container = breakdownRef.value
  const scrollLeft = container.scrollLeft
  const containerWidth = container.offsetWidth
  const scrollWidth = container.scrollWidth

  // 计算当前页面
  const totalPages = Math.ceil(scrollWidth / containerWidth)
  const currentPage = Math.round(scrollLeft / (scrollWidth - containerWidth) * (totalPages - 1))

  currentIndicator.value = Math.max(0, Math.min(currentPage, indicators.value.length - 1))
}

// 加载账户数据
const loadAccounts = async () => {
  loading.value = true
  try {
    await accountsStore.fetchAccountsWithBalances()
  } catch (error) {
    console.error('加载账户失败:', error)
    showToast({
      message: '加载账户失败',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}

// 格式化账户类型
const formatAccountType = (type) => {
  const typeMap = {
    bank: '银行账户',
    cash: '现金',
    credit: '信用卡',
    investment: '投资账户',
    ewallet: '电子钱包',
    other: '其他'
  }
  return typeMap[type] || type
}

// 获取账户图标
const getAccountIcon = (type) => {
  const icons = {
    bank: '🏦',
    cash: '💵',
    credit: '💳',
    investment: '📈',
    ewallet: '🏪',
    other: '💰'
  }
  return icons[type] || '💰'
}

// 查看账户详情
const viewAccount = (account) => {
  router.push(`/account/detail/${account.id}`)
}

// 选择货币
const selectCurrency = (currency) => {
  selectedCurrency.value = currency
  localStorage.setItem('accounts-currency', currency)
  showCurrencyPicker.value = false
}

// 格式化金额（不带货币符号）
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>

<style scoped>
.accounts-page {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  min-height: 100vh;
  padding-bottom: 20px;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.currency-btn {
  min-width: 50px;
  height: 28px;
  font-size: 12px;
  font-weight: 600;
}

.total-assets-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #1e293b;
  padding: 24px;
  margin: 16px;
  border-radius: 24px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(30, 58, 138, 0.15),
    0 8px 16px rgba(30, 58, 138, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.total-assets-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20%;
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba(30, 58, 138, 0.08) 0%, transparent 70%);
  border-radius: 50%;
}

.total-assets-card::after {
  content: '';
  position: absolute;
  bottom: -30%;
  left: -15%;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(55, 48, 163, 0.06) 0%, transparent 70%);
  border-radius: 50%;
}

.assets-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.assets-title {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.assets-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e40af;
  letter-spacing: 0.3px;
}

.assets-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  color: #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  padding: 4px 10px;
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.2);
  width: fit-content;
}

.assets-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(30, 64, 175, 0.3);
}

.assets-icon .van-icon {
  color: white;
}

.total-amount {
  margin-bottom: 24px;
  display: flex;
  align-items: baseline;
  gap: 6px;
  position: relative;
  z-index: 1;
}

.currency {
  font-size: 20px;
  font-weight: 600;
  color: #64748b;
}

.amount {
  font-size: 36px;
  font-weight: 800;
  color: #0f172a;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  letter-spacing: -0.5px;
}

.assets-breakdown-container {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.assets-breakdown {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding-bottom: 4px;
}

.assets-breakdown::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.breakdown-item {
  min-width: 140px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.7);
  padding: 16px 14px;
  border-radius: 16px;
  border: 1px solid rgba(30, 58, 138, 0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.breakdown-item.clickable {
  cursor: pointer;
}

.breakdown-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.breakdown-item.cash .breakdown-icon {
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.breakdown-item.bank .breakdown-icon {
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.breakdown-item.investment .breakdown-icon {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.breakdown-item.ewallet .breakdown-icon {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

.breakdown-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(30, 58, 138, 0.15);
}

.breakdown-item:active {
  transform: scale(0.98);
}

/* 滑动指示器 */
.breakdown-indicators {
  display: flex;
  justify-content: center;
  gap: 6px;
  margin-top: 12px;
}

.indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.indicator.active {
  background: rgba(255, 255, 255, 0.8);
  transform: scale(1.2);
}

.breakdown-icon {
  width: 32px;
  height: 32px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.breakdown-info {
  display: flex;
  flex-direction: column;
  gap: 3px;
  min-width: 0;
  flex: 1;
}

.breakdown-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.breakdown-value {
  font-size: 14px;
  font-weight: 700;
  color: #1e293b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  letter-spacing: -0.3px;
}

.assets-actions {
  display: flex;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.assets-actions .van-button {
  flex: 1;
  height: 40px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 13px;
  letter-spacing: 0.2px;
  transition: all 0.3s ease;
}

.assets-actions .van-button:first-child {
  background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
  border: 1px solid rgba(30, 64, 175, 0.3);
  color: white;
  box-shadow: 0 6px 16px rgba(30, 64, 175, 0.3);
}

.assets-actions .van-button:first-child:active {
  transform: translateY(1px);
  box-shadow: 0 3px 10px rgba(30, 64, 175, 0.4);
}

.assets-actions .van-button:last-child {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(30, 58, 138, 0.2);
  color: #1e40af;
  backdrop-filter: blur(10px);
}

.assets-actions .van-button:last-child:active {
  transform: translateY(1px);
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(30, 58, 138, 0.3);
}

.account-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid rgba(30, 58, 138, 0.2);
}

/* 响应式优化 */
@media (max-width: 375px) {
  .total-assets-card {
    margin: 12px;
    padding: 20px;
    border-radius: 20px;
  }

  .assets-header {
    margin-bottom: 20px;
  }

  .assets-icon {
    width: 40px;
    height: 40px;
    border-radius: 14px;
  }

  .assets-title h3 {
    font-size: 16px;
  }

  .total-amount {
    margin-bottom: 20px;
  }

  .amount {
    font-size: 32px;
  }

  .currency {
    font-size: 18px;
  }

  .assets-breakdown-container {
    margin-bottom: 20px;
  }

  .assets-breakdown {
    gap: 8px;
  }

  .breakdown-item {
    min-width: 120px;
    padding: 12px 10px;
    border-radius: 14px;
    gap: 8px;
  }

  .breakdown-icon {
    width: 28px;
    height: 28px;
    border-radius: 8px;
  }

  .breakdown-value {
    font-size: 13px;
  }

  .breakdown-label {
    font-size: 11px;
  }

  .assets-actions {
    gap: 10px;
  }

  .assets-actions .van-button {
    height: 40px;
    font-size: 13px;
    border-radius: 12px;
  }
}

.currency-picker {
  padding: 20px 0;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 16px;
  border-bottom: 1px solid #ebedf0;
}

.picker-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #323233;
}

@media (min-width: 768px) {
  .accounts-page {
    max-width: 480px;
    margin: 0 auto;
  }
}
</style>
