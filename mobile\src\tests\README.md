# 货币配置测试指南

## 概述

本目录包含了货币配置修改后的测试文件，用于验证系统只保留MYR和USD两种货币的正确性。

## 测试文件

### currency-config.test.js
完整的货币配置单元测试，包括：
- 货币数量验证
- 货币符号映射验证
- 各种格式化函数验证
- 级联选择逻辑验证

## 手动测试建议

### 1. 交易管理功能测试

#### 添加交易测试
1. 打开交易添加页面
2. 点击货币选择器
3. **验证**: 只显示"MYR - 马来西亚林吉特"和"USD - 美元"两个选项
4. 选择MYR货币
5. 点击账户选择器
6. **验证**: 只显示货币为MYR的账户
7. 选择USD货币
8. 点击账户选择器
9. **验证**: 只显示货币为USD的账户

#### KDI出金功能测试
1. 选择KDI账户
2. 选择支出类型
3. 选择"出金"分类
4. **验证**: 出金账户选择器只显示MYR货币的银行账户和电子钱包

### 2. 账户管理功能测试

#### 账户添加测试
1. 打开账户添加页面
2. 点击货币选择器
3. **验证**: 只显示MYR和USD两个选项
4. 默认货币应该是MYR

#### 账户列表测试
1. 打开账户列表页面
2. 点击货币筛选按钮
3. **验证**: 只显示MYR和USD两个选项
4. 选择不同货币，验证账户列表正确筛选

### 3. 预算管理功能测试

#### 预算添加测试
1. 打开预算添加页面
2. 点击货币选择器
3. **验证**: 只显示MYR和USD两个选项

### 4. 订阅管理功能测试

#### 订阅添加测试
1. 打开订阅添加页面
2. 点击货币选择器
3. **验证**: 显示格式为"货币名称 (代码)"的选项，只有MYR和USD

### 5. 仪表板功能测试

#### 货币筛选测试
1. 打开仪表板页面
2. 点击货币选择按钮
3. **验证**: 只显示MYR和USD两个选项
4. 切换货币，验证统计数据正确更新

## 验证清单

- [ ] 交易添加页面货币选择器只显示MYR和USD
- [ ] 交易编辑页面货币选择器只显示MYR和USD
- [ ] 账户添加页面货币选择器只显示MYR和USD
- [ ] 账户编辑页面货币选择器只显示MYR和USD
- [ ] 账户列表页面货币筛选只显示MYR和USD
- [ ] 预算添加页面货币选择器只显示MYR和USD
- [ ] 订阅添加页面货币选择器只显示MYR和USD
- [ ] 订阅编辑页面货币选择器只显示MYR和USD
- [ ] 仪表板页面货币筛选只显示MYR和USD
- [ ] 投资页面货币筛选只显示MYR和USD
- [ ] 货币-账户级联选择逻辑正常工作
- [ ] KDI出金账户只显示MYR的银行和电子钱包账户
- [ ] 默认货币为MYR
- [ ] 货币符号显示正确（MYR显示RM，USD显示$）

## 注意事项

1. **数据一致性**: 确保现有数据库中的交易、账户等记录不会因为货币配置修改而出现问题
2. **向后兼容**: 如果数据库中存在其他货币的记录，系统应该能够正常处理
3. **错误处理**: 验证系统对无效货币代码的处理是否正确

## 运行验证脚本

```bash
# 在mobile目录下运行
node verify-currency-config.js
```

这个脚本会自动验证货币配置的正确性和级联选择逻辑。
