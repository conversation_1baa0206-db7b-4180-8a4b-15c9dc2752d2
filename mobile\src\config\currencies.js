/**
 * 货币配置
 * 统一管理系统中所有货币相关的配置
 */

// 支持的货币列表 - 只保留马币(MYR)和美元(USD)
export const supportedCurrencies = [
  {
    code: 'MYR',
    name: '马来西亚林吉特',
    symbol: 'RM',
    text: 'MYR - 马来西亚林吉特'
  },
  {
    code: 'USD', 
    name: '美元',
    symbol: '$',
    text: 'USD - 美元'
  }
]

// 货币符号映射
export const currencySymbols = {
  'MYR': 'RM',
  'USD': '$'
}

// 获取货币符号
export const getCurrencySymbol = (currency) => {
  return currencySymbols[currency] || currency
}

// 获取货币名称
export const getCurrencyName = (currency) => {
  const currencyMap = {
    'MYR': 'MYR - 马来西亚林吉特',
    'USD': 'USD - 美元'
  }
  return currencyMap[currency] || currency
}

// 格式化为选择器选项
export const getCurrencyOptions = () => {
  return supportedCurrencies.map(currency => ({
    text: currency.text,
    value: currency.code
  }))
}

// 格式化为账户页面选项
export const getCurrencyOptionsForAccounts = () => {
  return supportedCurrencies.map(currency => ({
    code: currency.code,
    name: currency.name,
    symbol: currency.symbol
  }))
}

// 格式化为订阅页面选项
export const getCurrencyOptionsForSubscriptions = () => {
  return supportedCurrencies.map(currency => ({
    text: `${currency.name} (${currency.code})`,
    value: currency.code
  }))
}

// 默认货币
export const DEFAULT_CURRENCY = 'MYR'

// 验证货币代码是否有效
export const isValidCurrency = (currency) => {
  return supportedCurrencies.some(c => c.code === currency)
}
