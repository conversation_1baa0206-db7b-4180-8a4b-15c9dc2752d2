/**
 * 个人财务管理系统 - 主JavaScript文件
 */

// 全局变量，用于存储图表实例
let incomeExpenseChart = null;
let expenseCategoryChart = null;

// 全局用户状态
let currentUser = null;

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('应用程序初始化中...');

    // 首先检查登录状态
    checkLoginStatus().then(isLoggedIn => {
        if (!isLoggedIn) {
            // 未登录，跳转到登录页
            window.location.href = '/templates/auth/login.html';
            return;
        }

        // 已登录，继续初始化应用
        initApp();

        // 设置当前日期
        setCurrentDate();

        // 初始化菜单事件
        initMenuEvents();

        // 初始化类别选项
        initCategories();

        // 初始化货币选项
        initCurrencies();

        // 初始化交易表单
        initTransactionForm();

        // 初始化账户表单
        initAccountForm();

        // 初始化预算表单
        initBudgetForm();

        // 初始化目标表单
        initGoalForm();

        // 初始化设置页面
        initSettingsPage();

        // 初始化用户界面
        initUserInterface();

        // 加载交易数据
        loadTransactions();

        // 加载账户数据
        loadAccounts();

        // 加载预算数据
        loadBudgets();

        // 加载目标数据
        loadGoals();

        // 初始化图表
        initCharts();

        // 初始化通知栏
        initNotificationBar();
    }).catch(error => {
        console.error('检查登录状态失败:', error);
        window.location.href = '/templates/auth/login.html';
    });
});

/**
 * 初始化应用程序
 */
function initApp() {
    console.log('初始化应用程序...');

    // 从localStorage加载设置
    const settings = JSON.parse(localStorage.getItem('financeAppSettings')) || {};

    // 应用已固定使用中文，无需国际化设置

    // 初始化仪表盘货币筛选器
    initDashboardCurrencyFilter();

    // 检查API服务器是否可用
    fetch('/api/transactions')
        .then(response => {
            if (response.ok) {
                console.log('API服务器连接成功');
            } else {
                console.error('API服务器连接失败');
                showNotification('无法连接到服务器，请检查后端服务是否运行', 'error');
            }
        })
        .catch(error => {
            console.error('API请求错误:', error);
            showNotification('无法连接到服务器，请检查后端服务是否运行', 'error');
        });
}

/**
 * 设置当前日期显示
 */
function setCurrentDate() {
    const dateElement = document.getElementById('currentDate');
    if (dateElement) {
        const now = new Date();
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        dateElement.textContent = now.toLocaleDateString('zh-CN', options);
    }
}

/**
 * 初始化菜单事件
 */
function initMenuEvents() {
    const menuItems = document.querySelectorAll('.menu-item');

    menuItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有菜单项的active类
            menuItems.forEach(mi => mi.classList.remove('active'));

            // 为当前点击的菜单项添加active类
            this.classList.add('active');

            // 获取目标页面ID
            const targetPageId = this.getAttribute('data-page');

            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示目标页面
            const targetPage = document.getElementById(targetPageId);
            if (targetPage) {
                targetPage.classList.add('active');

                // 更新顶部标题
                updateTopbarTitle(this.querySelector('span').textContent);

                // 根据页面类型重新加载数据
                if (targetPageId === 'budgetsPage') {
                    // 重新加载预算数据以确保进度是最新的
                    loadBudgets();
                } else if (targetPageId === 'goalsPage') {
                    // 重新加载目标数据
                    loadGoals();
                } else if (targetPageId === 'dashboardPage') {
                    // 重新加载仪表盘数据
                    loadTransactions();
                }
            }
        });
    });
}

/**
 * 更新顶部标题
 */
function updateTopbarTitle(title) {
    const topbarTitle = document.querySelector('.topbar-left h1');
    if (topbarTitle) {
        topbarTitle.textContent = title;
    }
}

/**
 * 初始化交易表单
 */
function initTransactionForm() {
    const transactionForm = document.getElementById('transactionForm');

    if (transactionForm) {
        // 设置默认日期为今天
        const dateInput = document.getElementById('transactionDate');
        if (dateInput) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }

        // 表单提交事件
        transactionForm.addEventListener('submit', function(event) {
            event.preventDefault();
            saveTransaction();
        });

        // 交易类型变更时更新类别选项
        const typeSelect = document.getElementById('transactionType');
        if (typeSelect) {
            typeSelect.addEventListener('change', function() {
                updateCategoryOptions(this.value);
            });
        }

        // 账户变更时自动设置对应的货币
        const accountSelect = document.getElementById('transactionAccount');
        const currencySelect = document.getElementById('transactionCurrency');

        if (accountSelect && currencySelect) {
            // 禁用货币选择器，使其不可手动更改
            currencySelect.disabled = true;

            // 添加账户变更事件
            accountSelect.addEventListener('change', function() {
                const selectedAccountId = this.value;

                // 获取账户信息
                fetch(`/api/accounts/${selectedAccountId}`)
                    .then(response => response.json())
                    .then(account => {
                        // 设置对应的货币
                        currencySelect.value = account.currency;
                    })
                    .catch(error => {
                        console.error('获取账户信息失败:', error);
                    });
            });
        }

        // 附件预览功能
        const attachmentInput = document.getElementById('transactionAttachment');
        const attachmentPreview = document.getElementById('attachmentPreview');

        if (attachmentInput && attachmentPreview) {
            attachmentInput.addEventListener('change', function() {
                // 清空预览区域
                attachmentPreview.innerHTML = '';

                if (this.files && this.files.length > 0) {
                    const file = this.files[0];

                    // 检查文件类型
                    if (file.type.startsWith('image/')) {
                        // 图片预览
                        const img = document.createElement('img');
                        img.classList.add('attachment-thumbnail');
                        img.file = file;
                        attachmentPreview.appendChild(img);

                        const reader = new FileReader();
                        reader.onload = (function(aImg) {
                            return function(e) {
                                aImg.src = e.target.result;
                            };
                        })(img);
                        reader.readAsDataURL(file);
                    } else {
                        // 非图片文件显示文件名
                        const fileInfo = document.createElement('div');
                        fileInfo.classList.add('file-info');
                        fileInfo.innerHTML = `<span class="file-icon">📄</span> ${file.name}`;
                        attachmentPreview.appendChild(fileInfo);
                    }
                }
            });
        }

        // 添加导入/导出按钮事件监听
        initTransactionImportExport();
    }
}

/**
 * 根据交易类型更新类别选项
 */
function updateCategoryOptions(transactionType) {
    const categorySelect = document.getElementById('transactionCategory');

    if (categorySelect) {
        // 清空现有选项
        categorySelect.innerHTML = '<option value="" data-i18n="please_select">请选择</option>';

        // 根据交易类型添加相应的类别
        if (transactionType === 'income') {
            // 收入类别
            const incomeCategories = [
                '工资', '奖金', '投资收益', '礼金', '退款', '其他收入'
            ];

            incomeCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });
        } else if (transactionType === 'expense') {
            // 支出类别
            const expenseCategories = [
                '储蓄', '固定', '流动', '债务'
            ];

            expenseCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                categorySelect.appendChild(option);
            });
        }
    }
}

/**
 * 初始化类别选项
 */
function initCategories() {
    // 默认显示空类别列表
    updateCategoryOptions('');
}

/**
 * 初始化货币选项
 */
function initCurrencies() {
    const currencySelects = document.querySelectorAll('select[id$="Currency"]');

    const currencies = [
        { code: 'MYR', name: '马来西亚林吉特 (RM)' },
        { code: 'USD', name: '美元 ($)' }
    ];

    currencySelects.forEach(select => {
        // 清空现有选项
        select.innerHTML = '';

        // 添加货币选项
        currencies.forEach(currency => {
            const option = document.createElement('option');
            option.value = currency.code;
            option.textContent = currency.name;
            select.appendChild(option);
        });

        // 默认选择马来西亚林吉特
        select.value = 'MYR';
    });
}

/**
 * 加载交易数据
 */
function loadTransactions() {
    fetch('/api/transactions')
        .then(response => response.json())
        .then(transactions => {
            displayTransactions(transactions);
            displayRecentTransactions(transactions);
            updateDashboardStats(transactions);
        })
        .catch(error => {
            console.error('加载交易数据失败:', error);
            showNotification('加载交易数据失败', 'error');
        });
}

/**
 * 初始化仪表盘货币筛选器
 */
function initDashboardCurrencyFilter() {
    const currencySelect = document.getElementById('dashboardCurrency');

    if (currencySelect) {
        // 添加货币选项
        const currencies = [
            { code: 'ALL', name: '所有货币' },
            { code: 'MYR', name: '马来西亚林吉特 (RM)' },
            { code: 'CNY', name: '人民币 (¥)' },
            { code: 'USD', name: '美元 ($)' },
            { code: 'EUR', name: '欧元 (€)' },
            { code: 'GBP', name: '英镑 (£)' },
            { code: 'JPY', name: '日元 (¥)' },
            { code: 'KRW', name: '韩元 (₩)' },
            { code: 'SGD', name: '新加坡元 (S$)' }
        ];

        // 清空现有选项
        currencySelect.innerHTML = '';

        // 添加货币选项
        currencies.forEach(currency => {
            const option = document.createElement('option');
            option.value = currency.code;
            option.textContent = currency.name;
            currencySelect.appendChild(option);
        });

        // 默认选择所有货币
        currencySelect.value = 'ALL';

        // 添加变更事件
        currencySelect.addEventListener('change', function() {
            // 获取当前的交易数据，并按选定的货币筛选更新显示
            fetch('/api/transactions')
                .then(response => response.json())
                .then(transactions => {
                    updateDashboardStats(transactions);
                    // 手动更新最近交易列表，因为updateDashboardStats中已经移除了这个调用
                    const selectedCurrency = this.value;
                    let filteredTransactions = transactions;
                    if (selectedCurrency !== 'ALL') {
                        filteredTransactions = transactions.filter(tx => tx.currency === selectedCurrency);
                    }
                    displayRecentTransactions(filteredTransactions);
                })
                .catch(error => {
                    console.error('更新货币筛选失败:', error);
                });
        });
    }
}

/**
 * 更新Dashboard统计数据
 */
function updateDashboardStats(transactions) {
    // 获取选定的货币
    const selectedCurrency = document.getElementById('dashboardCurrency')?.value || 'ALL';

    // 根据选定的货币筛选交易
    let filteredTransactions = transactions;
    if (selectedCurrency !== 'ALL') {
        filteredTransactions = transactions.filter(tx => tx.currency === selectedCurrency);
    }

    // 计算总收入
    const totalIncome = filteredTransactions
        .filter(tx => tx.type === 'income')
        .reduce((sum, tx) => sum + parseFloat(tx.amount), 0);

    // 计算总支出
    const totalExpense = filteredTransactions
        .filter(tx => tx.type === 'expense')
        .reduce((sum, tx) => sum + parseFloat(tx.amount), 0);

    // 计算净资产
    const netWorth = totalIncome - totalExpense;

    // 更新UI - 使用选定的货币或默认货币
    const displayCurrency = selectedCurrency === 'ALL' ? 'MYR' : selectedCurrency;
    document.getElementById('totalIncome').textContent = formatCurrency(totalIncome, displayCurrency);
    document.getElementById('totalExpense').textContent = formatCurrency(totalExpense, displayCurrency);
    document.getElementById('netWorth').textContent = formatCurrency(netWorth, displayCurrency);

    // 模拟与上月的变化（实际应用中应该从数据库获取上月数据）
    document.getElementById('incomeChange').textContent = '+5%';
    document.getElementById('expenseChange').textContent = '+3%';
    document.getElementById('netWorthChange').textContent = '+7%';

    // 更新图表
    updateCharts(filteredTransactions);

    // 注意：不在这里更新最近交易列表，避免重复调用
    // displayRecentTransactions 已经在 loadTransactions 中调用了
}

/**
 * 初始化图表
 */
function initCharts() {
    // 收支趋势图表
    const incomeExpenseCtx = document.getElementById('incomeExpenseChart');
    if (incomeExpenseCtx) {
        // 获取最近6个月的标签
        const labels = getLast6MonthsLabels();

        incomeExpenseChart = new Chart(incomeExpenseCtx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '收入',
                        data: Array(6).fill(0),
                        borderColor: '#3a5a8c',
                        backgroundColor: 'rgba(58, 90, 140, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '支出',
                        data: Array(6).fill(0),
                        borderColor: '#5d7185',
                        backgroundColor: 'rgba(93, 113, 133, 0.05)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // 支出分类图表
    const expenseCategoryCtx = document.getElementById('expenseCategoryChart');
    if (expenseCategoryCtx) {
        expenseCategoryChart = new Chart(expenseCategoryCtx, {
            type: 'doughnut',
            data: {
                labels: [],
                datasets: [{
                    data: [],
                    backgroundColor: [
                        '#3a5a8c', '#4a6a9c', '#5a7aac', '#6a8abc', '#7a9acc',
                        '#8aaadc', '#9abaec', '#aacafc', '#badaff', '#caeaff'
                    ],
                    hoverOffset: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    }
}

/**
 * 获取最近6个月的标签
 */
function getLast6MonthsLabels() {
    const labels = [];
    const today = new Date();

    // 获取最近6个月（包括当月）
    for (let i = 5; i >= 0; i--) {
        const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthLabel = `${month.getMonth() + 1}月`;
        labels.push(monthLabel);
    }

    return labels;
}

/**
 * 更新图表数据
 */
function updateCharts(transactions) {
    if (!transactions || transactions.length === 0) {
        // 如果没有交易数据，至少更新图表标签
        if (incomeExpenseChart) {
            incomeExpenseChart.data.labels = getLast6MonthsLabels();
            incomeExpenseChart.update();
        }
        return;
    }

    // 获取最近6个月的月份键（YYYY-MM格式）
    const monthKeys = [];
    const today = new Date();

    for (let i = 5; i >= 0; i--) {
        const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthKey = `${month.getFullYear()}-${String(month.getMonth() + 1).padStart(2, '0')}`;
        monthKeys.push(monthKey);
    }

    // 按月份分组交易
    const monthlyData = groupTransactionsByMonth(transactions);

    // 确保所有月份都有数据
    monthKeys.forEach(month => {
        if (!monthlyData[month]) {
            monthlyData[month] = { income: 0, expense: 0 };
        }
    });

    // 更新收支趋势图表
    if (incomeExpenseChart) {
        // 根据月份键获取收入和支出数据
        const incomeData = monthKeys.map(month => monthlyData[month].income || 0);
        const expenseData = monthKeys.map(month => monthlyData[month].expense || 0);

        // 更新图表数据
        incomeExpenseChart.data.labels = getLast6MonthsLabels();
        incomeExpenseChart.data.datasets[0].data = incomeData;
        incomeExpenseChart.data.datasets[1].data = expenseData;
        incomeExpenseChart.update();
    }

    // 更新支出分类图表
    if (expenseCategoryChart) {
        // 按类别分组支出
        const categoryData = groupExpensesByCategory(transactions);
        const categories = Object.keys(categoryData);
        const amounts = categories.map(category => categoryData[category]);

        expenseCategoryChart.data.labels = categories;
        expenseCategoryChart.data.datasets[0].data = amounts;
        expenseCategoryChart.update();
    }
}

/**
 * 按月份分组交易
 */
function groupTransactionsByMonth(transactions) {
    const monthlyData = {};

    transactions.forEach(tx => {
        const date = new Date(tx.date);
        const month = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        if (!monthlyData[month]) {
            monthlyData[month] = { income: 0, expense: 0 };
        }

        if (tx.type === 'income') {
            monthlyData[month].income += parseFloat(tx.amount);
        } else if (tx.type === 'expense') {
            monthlyData[month].expense += parseFloat(tx.amount);
        }
    });

    return monthlyData;
}

/**
 * 按类别分组支出
 */
function groupExpensesByCategory(transactions) {
    const categoryData = {};

    // 只处理支出类型的交易
    transactions
        .filter(tx => tx.type === 'expense')
        .forEach(tx => {
            if (!categoryData[tx.category]) {
                categoryData[tx.category] = 0;
            }
            categoryData[tx.category] += parseFloat(tx.amount);
        });

    return categoryData;
}

/**
 * 按货币分组交易
 */
function groupTransactionsByCurrency(transactions) {
    const currencyData = {};

    transactions.forEach(tx => {
        if (!currencyData[tx.currency]) {
            currencyData[tx.currency] = {
                income: 0,
                expense: 0
            };
        }

        if (tx.type === 'income') {
            currencyData[tx.currency].income += parseFloat(tx.amount);
        } else if (tx.type === 'expense') {
            currencyData[tx.currency].expense += parseFloat(tx.amount);
        }
    });

    return currencyData;
}

/**
 * 格式化月份标签
 */
function formatMonthLabel(monthStr) {
    const [, month] = monthStr.split('-');
    return `${month}月`;
}

/**
 * 显示交易列表
 */
function displayTransactions(transactions) {
    const transactionsList = document.getElementById('transactionsList');

    if (transactionsList) {
        // 清空现有内容
        transactionsList.innerHTML = '';

        if (transactions.length === 0) {
            // 没有交易记录
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="8" class="empty-message">暂无交易记录</td>`;
            transactionsList.appendChild(emptyRow);
            return;
        }

        // 获取账户数据，用于显示账户名称
        fetch('/api/accounts')
            .then(response => response.json())
            .then(accounts => {
                // 创建账户ID到名称的映射
                const accountMap = {};
                accounts.forEach(account => {
                    accountMap[account.id] = account.name;
                });

                // 按日期排序（最新的在前）
                transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

                // 添加交易记录
                transactions.forEach(transaction => {
                    const row = document.createElement('tr');

                    // 设置行的类名，用于样式区分
                    row.className = transaction.type === 'income' ? 'income-row' : 'expense-row';

                    // 格式化金额显示
                    const formattedAmount = formatCurrency(transaction.amount, transaction.currency);

                    // 获取账户名称（如果存在映射）
                    const accountName = accountMap[transaction.account] || transaction.account;

                    // 直接使用中文文本，无需翻译

                    // 设置附件图标和点击事件
                    let attachmentCell = '';
                    if (transaction.attachment) {
                        attachmentCell = `<a href="javascript:void(0)" class="attachment-link" data-id="${transaction.id}">📎</a>`;
                    } else {
                        attachmentCell = '';
                    }

                    // 设置行内容
                    const typeText = transaction.type === 'income' ? '收入' : '支出';
                    row.innerHTML = `
                        <td>${formatDate(transaction.date)}</td>
                        <td>${typeText}</td>
                        <td>${transaction.category}</td>
                        <td>${transaction.description}</td>
                        <td>${accountName}</td>
                        <td class="${transaction.type === 'income' ? 'income-amount' : 'expense-amount'}">${formattedAmount}</td>
                        <td>${attachmentCell}</td>
                        <td>
                            <button class="action-btn edit-btn" data-id="${transaction.id}" title="编辑">✏️</button>
                            <button class="action-btn delete-btn" data-id="${transaction.id}" title="删除">🗑️</button>
                        </td>
                    `;

                    transactionsList.appendChild(row);
                });

                // 添加编辑和删除按钮的事件监听
                addTransactionButtonListeners();

                // 添加附件链接的事件监听
                addAttachmentLinkListeners();
            })
            .catch(error => {
                console.error('获取账户数据失败:', error);

                // 如果获取账户数据失败，仍然显示交易记录，但使用原始账户ID
                displayTransactionsWithoutAccountNames(transactions);
            });
    }
}

/**
 * 初始化交易导入导出功能
 */
function initTransactionImportExport() {
    // 导出CSV按钮
    const exportBtn = document.getElementById('exportTransactionsBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', exportTransactionsCSV);
    }

    // 导入CSV按钮
    const importBtn = document.getElementById('importTransactionsBtn');
    if (importBtn) {
        importBtn.addEventListener('click', importTransactionsCSV);
    }
}

/**
 * 导出交易记录为CSV
 */
function exportTransactionsCSV() {
    // 直接从API获取CSV文件
    window.location.href = '/api/transactions/export/csv';
    showNotification('正在导出交易记录...', 'info');
}

/**
 * 导入交易记录CSV
 */
function importTransactionsCSV() {
    // 创建文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.csv';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);

    // 监听文件选择事件
    fileInput.addEventListener('change', function() {
        if (fileInput.files.length === 0) {
            document.body.removeChild(fileInput);
            return;
        }

        const file = fileInput.files[0];

        // 检查文件类型
        if (!file.name.endsWith('.csv')) {
            showNotification('请选择CSV文件', 'error');
            document.body.removeChild(fileInput);
            return;
        }

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);

        // 显示加载中通知
        showNotification('正在导入交易记录...', 'info');

        // 发送到服务器
        fetch('/api/transactions/import/csv', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`导入成功: ${data.message}`, 'success');
                // 重新加载交易数据
                loadTransactions();
            } else {
                showNotification(`导入失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('导入CSV失败:', error);
            showNotification('导入失败，请稍后重试', 'error');
        })
        .finally(() => {
            // 清理文件输入元素
            document.body.removeChild(fileInput);
        });
    });

    // 触发文件选择对话框
    fileInput.click();
}

/**
 * 在无法获取账户名称时显示交易列表
 */
function displayTransactionsWithoutAccountNames(transactions) {
    const transactionsList = document.getElementById('transactionsList');

    if (!transactionsList) return;

    // 按日期排序（最新的在前）
    transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

    // 添加交易记录
    transactions.forEach(transaction => {
        const row = document.createElement('tr');

        // 不再使用不同的样式区分收入和支出
        row.className = 'transaction-row';

        // 格式化金额显示
        const formattedAmount = formatCurrency(transaction.amount, transaction.currency);

        // 直接使用中文文本，无需翻译

        // 设置行内容
        row.innerHTML = `
            <td>${formatDate(transaction.date)}</td>
            <td>${transaction.type === 'income' ? '收入' : '支出'}</td>
            <td>${transaction.category}</td>
            <td>${transaction.description}</td>
            <td>${transaction.account}</td>
            <td>${formattedAmount}</td>
            <td>${transaction.attachment ? '📎' : ''}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${transaction.id}" title="编辑">✏️</button>
                <button class="action-btn delete-btn" data-id="${transaction.id}" title="删除">🗑️</button>
            </td>
        `;

        transactionsList.appendChild(row);
    });

    // 添加编辑和删除按钮的事件监听
    addTransactionButtonListeners();
}

/**
 * 显示最近交易
 */
function displayRecentTransactions(transactions) {
    const recentTransactionsList = document.getElementById('recentTransactionsList');

    if (recentTransactionsList) {
        // 清空现有内容
        recentTransactionsList.innerHTML = '';

        if (transactions.length === 0) {
            // 没有交易记录
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="5" class="empty-message">暂无交易记录</td>`;
            recentTransactionsList.appendChild(emptyRow);
            return;
        }

        // 获取账户数据，用于显示账户名称
        fetch('/api/accounts')
            .then(response => response.json())
            .then(accounts => {
                // 创建账户ID到名称的映射
                const accountMap = {};
                accounts.forEach(account => {
                    accountMap[account.id] = account.name;
                });

                // 按日期排序（最新的在前）
                transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

                // 只显示最近5条记录
                const recentTransactions = transactions.slice(0, 5);

                // 添加交易记录
                recentTransactions.forEach(transaction => {
                    const row = document.createElement('tr');

                    // 不再使用不同的样式区分收入和支出
                    row.className = 'transaction-row';

                    // 格式化金额显示
                    const formattedAmount = formatCurrency(transaction.amount, transaction.currency);

                    // 设置行内容
                    const typeText = transaction.type === 'income' ? '收入' : '支出';
                    row.innerHTML = `
                        <td>${formatDate(transaction.date)}</td>
                        <td>${typeText}</td>
                        <td>${transaction.category}</td>
                        <td>${transaction.description}</td>
                        <td>${formattedAmount}</td>
                    `;

                    recentTransactionsList.appendChild(row);
                });
            })
            .catch(error => {
                console.error('获取账户数据失败:', error);

                // 如果获取账户数据失败，仍然显示交易记录
                displayRecentTransactionsWithoutAccountNames(transactions);
            });
    }
}

/**
 * 在无法获取账户名称时显示最近交易
 */
function displayRecentTransactionsWithoutAccountNames(transactions) {
    const recentTransactionsList = document.getElementById('recentTransactionsList');

    if (!recentTransactionsList) return;

    // 清空现有内容，避免重复显示
    recentTransactionsList.innerHTML = '';

    if (transactions.length === 0) {
        // 没有交易记录
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="5" class="empty-message">暂无交易记录</td>`;
        recentTransactionsList.appendChild(emptyRow);
        return;
    }

    // 按日期排序（最新的在前）
    transactions.sort((a, b) => new Date(b.date) - new Date(a.date));

    // 只显示最近5条记录
    const recentTransactions = transactions.slice(0, 5);

    // 添加交易记录
    recentTransactions.forEach(transaction => {
        const row = document.createElement('tr');

        // 不再使用不同的样式区分收入和支出
        row.className = 'transaction-row';

        // 格式化金额显示
        const formattedAmount = formatCurrency(transaction.amount, transaction.currency);

        // 直接使用中文文本，无需翻译

        // 设置行内容
        const typeText = transaction.type === 'income' ? '收入' : '支出';
        row.innerHTML = `
            <td>${formatDate(transaction.date)}</td>
            <td>${typeText}</td>
            <td>${transaction.category}</td>
            <td>${transaction.description}</td>
            <td>${formattedAmount}</td>
        `;

        recentTransactionsList.appendChild(row);
    });
}

/**
 * 添加交易按钮的事件监听
 */
function addTransactionButtonListeners() {
    // 编辑按钮
    const editButtons = document.querySelectorAll('.edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            editTransaction(transactionId);
        });
    });

    // 删除按钮
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            deleteTransaction(transactionId);
        });
    });
}

/**
 * 添加附件链接的事件监听
 */
function addAttachmentLinkListeners() {
    // 附件链接
    const attachmentLinks = document.querySelectorAll('.attachment-link');
    attachmentLinks.forEach(link => {
        link.addEventListener('click', function() {
            const transactionId = this.getAttribute('data-id');
            viewAttachment(transactionId);
        });
    });
}

/**
 * 查看附件
 */
function viewAttachment(transactionId) {
    // 获取交易详情
    fetch(`/api/transactions/${transactionId}`)
        .then(response => response.json())
        .then(transaction => {
            if (transaction.attachment) {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'modal';

                // 创建模态框内容
                const modalContent = document.createElement('div');
                modalContent.className = 'modal-content';

                // 创建关闭按钮
                const closeBtn = document.createElement('span');
                closeBtn.className = 'close-btn';
                closeBtn.innerHTML = '&times;';
                closeBtn.onclick = function() {
                    document.body.removeChild(modal);
                };

                // 添加关闭按钮到模态框内容
                modalContent.appendChild(closeBtn);

                // 检查附件类型
                if (transaction.attachment.startsWith('data:image/')) {
                    // 图片附件
                    const img = document.createElement('img');
                    img.className = 'attachment-image';
                    img.src = transaction.attachment;
                    modalContent.appendChild(img);
                } else {
                    // 非图片附件
                    const fileInfo = document.createElement('div');
                    fileInfo.className = 'file-info';
                    fileInfo.innerHTML = '<span class="file-icon">📄</span> 附件';

                    // 添加下载链接
                    const downloadLink = document.createElement('a');
                    downloadLink.href = transaction.attachment;
                    downloadLink.download = `attachment_${transactionId}`;
                    downloadLink.className = 'download-link';
                    downloadLink.textContent = '下载附件';

                    modalContent.appendChild(fileInfo);
                    modalContent.appendChild(downloadLink);
                }

                // 添加模态框内容到模态框
                modal.appendChild(modalContent);

                // 添加模态框到页面
                document.body.appendChild(modal);

                // 点击模态框外部关闭模态框
                window.onclick = function(event) {
                    if (event.target === modal) {
                        document.body.removeChild(modal);
                    }
                };
            } else {
                showNotification('该交易没有附件', 'info');
            }
        })
        .catch(error => {
            console.error('获取附件失败:', error);
            showNotification('获取附件失败', 'error');
        });
}

/**
 * 保存交易
 */
function saveTransaction() {
    // 获取表单数据
    const transactionData = {
        date: document.getElementById('transactionDate').value,
        type: document.getElementById('transactionType').value,
        category: document.getElementById('transactionCategory').value,
        description: document.getElementById('transactionDescription').value,
        account: document.getElementById('transactionAccount').value,
        amount: parseFloat(document.getElementById('transactionAmount').value),
        currency: document.getElementById('transactionCurrency').value,
        attachment: '' // 初始化为空，稍后处理附件
    };

    // 验证必填字段
    if (!transactionData.date || !transactionData.type || !transactionData.category ||
        !transactionData.description || !transactionData.account || isNaN(transactionData.amount)) {
        showNotification('请填写所有必填字段', 'error');
        return;
    }

    // 检查是否是编辑模式
    const editId = document.getElementById('transactionForm').getAttribute('data-edit-id');
    if (editId) {
        transactionData.id = editId;
    }

    // 处理附件
    const attachmentInput = document.getElementById('transactionAttachment');
    if (attachmentInput.files && attachmentInput.files.length > 0) {
        const file = attachmentInput.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            // 将文件内容转换为Base64编码
            transactionData.attachment = e.target.result;

            // 发送到服务器
            sendTransactionData(transactionData);
        };

        // 读取文件为DataURL (Base64)
        reader.readAsDataURL(file);
    } else {
        // 没有附件，直接发送数据
        sendTransactionData(transactionData);
    }
}

/**
 * 发送交易数据到服务器
 */
function sendTransactionData(transactionData) {
    // 显示保存状态
    const saveButton = document.getElementById('saveTransaction');
    const originalText = saveButton.textContent;
    saveButton.textContent = '保存中...';
    saveButton.disabled = true;

    // 发送到服务器
    fetch('/api/transactions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(transactionData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('交易保存成功', 'success');
            // 重置表单
            document.getElementById('transactionForm').reset();
            document.getElementById('transactionForm').removeAttribute('data-edit-id');
            saveButton.textContent = '保存';
            // 设置默认日期为今天
            document.getElementById('transactionDate').value = new Date().toISOString().split('T')[0];

            // 检查预算超出情况（仅对支出进行检查，且不是编辑模式）
            if (transactionData.type === 'expense' && !transactionData.id) {
                checkBudgetAfterTransaction(transactionData);
            }

            // 优化：延迟加载数据，避免立即重复请求
            setTimeout(() => {
                loadTransactions();
            }, 500);
        } else {
            showNotification(`保存失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('保存交易失败:', error);
        showNotification('保存交易失败，请稍后重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        saveButton.disabled = false;
        if (saveButton.textContent === '保存中...') {
            saveButton.textContent = originalText;
        }
    });
}

/**
 * 编辑交易
 */
function editTransaction(transactionId) {
    // 获取交易详情
    fetch(`/api/transactions/${transactionId}`)
        .then(response => response.json())
        .then(transaction => {
            // 填充表单
            document.getElementById('transactionDate').value = transaction.date;
            document.getElementById('transactionType').value = transaction.type;

            // 更新类别选项
            updateCategoryOptions(transaction.type);

            // 设置类别值
            setTimeout(() => {
                document.getElementById('transactionCategory').value = transaction.category;
            }, 100);

            document.getElementById('transactionDescription').value = transaction.description;
            document.getElementById('transactionAccount').value = transaction.account;
            document.getElementById('transactionAmount').value = transaction.amount;

            // 获取账户信息，设置对应的货币
            fetch(`/api/accounts/${transaction.account}`)
                .then(response => response.json())
                .then(account => {
                    // 设置对应的货币
                    document.getElementById('transactionCurrency').value = account.currency;
                })
                .catch(error => {
                    console.error('获取账户信息失败:', error);
                    // 如果获取账户信息失败，使用交易记录中的货币
                    document.getElementById('transactionCurrency').value = transaction.currency;
                });

            // 处理附件预览
            const attachmentPreview = document.getElementById('attachmentPreview');
            if (attachmentPreview) {
                attachmentPreview.innerHTML = '';

                if (transaction.attachment) {
                    // 检查是否是图片附件
                    if (transaction.attachment.startsWith('data:image/')) {
                        // 图片预览
                        const img = document.createElement('img');
                        img.classList.add('attachment-thumbnail');
                        img.src = transaction.attachment;
                        attachmentPreview.appendChild(img);
                    } else {
                        // 非图片文件显示文件图标
                        const fileInfo = document.createElement('div');
                        fileInfo.classList.add('file-info');
                        fileInfo.innerHTML = '<span class="file-icon">📄</span> 附件';
                        attachmentPreview.appendChild(fileInfo);
                    }
                }
            }

            // 保存交易ID，用于更新
            document.getElementById('transactionForm').setAttribute('data-edit-id', transactionId);

            // 更改保存按钮文本
            document.getElementById('saveTransaction').textContent = '更新';

            // 滚动到表单
            document.getElementById('transactionForm').scrollIntoView({ behavior: 'smooth' });
        })
        .catch(error => {
            console.error('获取交易详情失败:', error);
            showNotification('获取交易详情失败', 'error');
        });
}

/**
 * 删除交易
 */
function deleteTransaction(transactionId) {
    if (confirm('确定要删除这条交易记录吗？此操作不可撤销。')) {
        fetch(`/api/transactions/${transactionId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('交易删除成功', 'success');
                // 重新加载交易数据
                loadTransactions();
            } else {
                showNotification(`删除失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('删除交易失败:', error);
            showNotification('删除交易失败，请稍后重试', 'error');
        });
    }
}

/**
 * 初始化账户表单
 */
function initAccountForm() {
    const accountForm = document.getElementById('accountForm');

    if (accountForm) {
        // 表单提交事件
        accountForm.addEventListener('submit', function(event) {
            event.preventDefault();
            saveAccount();
        });
    }
}

/**
 * 保存账户
 */
function saveAccount() {
    // 获取表单数据
    const accountData = {
        name: document.getElementById('accountName').value,
        type: document.getElementById('accountType').value,
        currency: document.getElementById('accountCurrency').value,
        initial_balance: parseFloat(document.getElementById('accountInitialBalance').value),
        description: document.getElementById('accountDescription').value
    };

    // 验证必填字段
    if (!accountData.name || !accountData.type || !accountData.currency || isNaN(accountData.initial_balance)) {
        showNotification('请填写所有必填字段', 'error');
        return;
    }

    // 获取编辑ID（如果有）
    const accountId = document.getElementById('accountForm').getAttribute('data-edit-id');
    if (accountId) {
        accountData.id = accountId;
    }

    // 发送到服务器
    fetch('/api/accounts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(accountData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('账户保存成功', 'success');
            // 重置表单
            document.getElementById('accountForm').reset();
            // 清除编辑ID
            document.getElementById('accountForm').removeAttribute('data-edit-id');
            // 恢复保存按钮文本
            document.getElementById('saveAccount').textContent = '保存';
            // 重新加载账户数据
            loadAccounts();
        } else {
            showNotification(`保存失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('保存账户失败:', error);
        showNotification('保存账户失败，请稍后重试', 'error');
    });
}

/**
 * 加载账户数据
 */
function loadAccounts() {
    fetch('/api/accounts')
        .then(response => response.json())
        .then(accounts => {
            // 更新账户选择框
            updateAccountOptions(accounts);
            // 显示账户列表（如果在账户页面）
            displayAccounts(accounts);
            // 计算并显示当前余额
            calculateAccountBalances(accounts);
        })
        .catch(error => {
            console.error('加载账户数据失败:', error);
            showNotification('加载账户数据失败', 'error');
        });
}

/**
 * 计算账户当前余额
 */
function calculateAccountBalances(accounts) {
    // 获取所有交易
    fetch('/api/transactions')
        .then(response => response.json())
        .then(transactions => {
            // 计算每个账户的当前余额
            accounts.forEach(account => {
                // 初始余额
                let balance = parseFloat(account.initial_balance);

                // 计算该账户的所有交易
                transactions.forEach(tx => {
                    if (tx.account === account.id) {
                        if (tx.type === 'income') {
                            balance += parseFloat(tx.amount);
                        } else if (tx.type === 'expense') {
                            balance -= parseFloat(tx.amount);
                        }
                    }
                });

                // 更新账户余额显示
                const balanceCell = document.querySelector(`#accountsList tr[data-id="${account.id}"] .current-balance`);
                if (balanceCell) {
                    balanceCell.textContent = formatCurrency(balance, account.currency);
                }
            });
        })
        .catch(error => {
            console.error('计算账户余额失败:', error);
        });
}

/**
 * 更新账户选择框
 */
function updateAccountOptions(accounts) {
    const accountSelect = document.getElementById('transactionAccount');

    if (accountSelect) {
        // 清空现有选项
        accountSelect.innerHTML = '';

        // 添加账户选项
        accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = account.name;
            accountSelect.appendChild(option);
        });

        // 如果没有账户，添加默认账户
        if (accounts.length === 0) {
            const option = document.createElement('option');
            option.value = 'default';
            option.textContent = '默认账户';
            accountSelect.appendChild(option);
        }
    }
}

/**
 * 显示账户列表
 */
function displayAccounts(accounts) {
    const accountsList = document.getElementById('accountsList');

    if (accountsList) {
        // 清空现有内容
        accountsList.innerHTML = '';

        if (accounts.length === 0) {
            // 没有账户记录
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="6" class="empty-message">暂无账户记录</td>`;
            accountsList.appendChild(emptyRow);
            return;
        }

        // 添加账户记录
        accounts.forEach(account => {
            const row = document.createElement('tr');
            row.setAttribute('data-id', account.id);

            // 格式化余额显示
            const formattedInitialBalance = formatCurrency(account.initial_balance, account.currency);

            // 直接使用中文文本，无需翻译

            // 设置行内容
            row.innerHTML = `
                <td class="account-name">${account.name}</td>
                <td class="account-type">${getAccountTypeName(account.type)}</td>
                <td class="account-currency">${account.currency}</td>
                <td class="account-initial-balance">${formattedInitialBalance}</td>
                <td class="account-balance current-balance">${formattedInitialBalance}</td>
                <td class="account-actions">
                    <button class="action-btn edit-btn" data-id="${account.id}" title="编辑">
                        <span class="action-icon">✏️</span>
                    </button>
                    <button class="action-btn delete-btn" data-id="${account.id}" title="删除">
                        <span class="action-icon">🗑️</span>
                    </button>
                </td>
            `;

            accountsList.appendChild(row);
        });

        // 添加账户按钮的事件监听
        addAccountButtonListeners();
    }
}

/**
 * 添加账户按钮的事件监听
 */
function addAccountButtonListeners() {
    // 编辑按钮
    const editButtons = document.querySelectorAll('#accountsList .edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const accountId = this.getAttribute('data-id');
            editAccount(accountId);
        });
    });

    // 删除按钮
    const deleteButtons = document.querySelectorAll('#accountsList .delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const accountId = this.getAttribute('data-id');
            deleteAccount(accountId);
        });
    });
}

/**
 * 编辑账户
 */
function editAccount(accountId) {
    // 获取账户详情
    fetch(`/api/accounts/${accountId}`)
        .then(response => response.json())
        .then(account => {
            // 填充表单
            document.getElementById('accountName').value = account.name;
            document.getElementById('accountType').value = account.type;
            document.getElementById('accountCurrency').value = account.currency;
            document.getElementById('accountInitialBalance').value = account.initial_balance;
            document.getElementById('accountDescription').value = account.description || '';

            // 保存账户ID，用于更新
            document.getElementById('accountForm').setAttribute('data-edit-id', accountId);

            // 更改保存按钮文本
            document.getElementById('saveAccount').textContent = '更新';

            // 滚动到表单
            document.getElementById('accountForm').scrollIntoView({ behavior: 'smooth' });
        })
        .catch(error => {
            console.error('获取账户详情失败:', error);
            showNotification('获取账户详情失败', 'error');
        });
}

/**
 * 删除账户
 */
function deleteAccount(accountId) {
    if (confirm('确定要删除这个账户吗？相关交易将转移到默认账户。此操作不可撤销。')) {
        fetch(`/api/accounts/${accountId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('账户删除成功', 'success');
                // 重新加载账户数据
                loadAccounts();
            } else {
                showNotification(`删除失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('删除账户失败:', error);
            showNotification('删除账户失败，请稍后重试', 'error');
        });
    }
}

/**
 * 初始化预算表单
 */
function initBudgetForm() {
    console.log('=== 初始化预算表单 ===');
    const budgetForm = document.getElementById('budgetForm');
    console.log('budgetForm 元素:', budgetForm);

    if (budgetForm) {
        console.log('找到预算表单，开始初始化...');

        // 初始化预算类别选择框
        initBudgetCategories();

        // 初始化年份选择框（即使隐藏也要初始化，避免表单验证错误）
        initBudgetYearSelector();

        // 确保日期选择器初始状态正确显示
        const specificMonthSelector = document.getElementById('specificMonthSelector');
        const dateRangeSelector = document.getElementById('dateRangeSelector');

        console.log('specificMonthSelector:', specificMonthSelector);
        console.log('dateRangeSelector:', dateRangeSelector);

        if (specificMonthSelector) {
            specificMonthSelector.style.display = 'none';
        }
        if (dateRangeSelector) {
            dateRangeSelector.style.display = 'flex';
        }

        // 设置默认周期为每月
        const periodSelect = document.getElementById('budgetPeriod');
        console.log('periodSelect:', periodSelect);
        if (periodSelect) {
            periodSelect.value = 'monthly';

            // 预算周期变更事件
            periodSelect.addEventListener('change', function() {
                console.log('周期变更为:', this.value);
                updateBudgetDateRange(this.value);
            });
        }

        // 初始化默认日期（每月）
        updateBudgetDateRange('monthly');

        // 表单提交事件
        console.log('绑定表单提交事件...');
        budgetForm.addEventListener('submit', function(event) {
            console.log('表单提交事件触发!');
            event.preventDefault();
            saveBudget();
        });

        console.log('预算表单初始化完成');
    } else {
        console.error('未找到预算表单元素!');
    }
}

/**
 * 初始化预算类别选择框
 */
function initBudgetCategories() {
    const categorySelect = document.getElementById('budgetCategory');

    if (categorySelect) {
        // 清空现有选项
        categorySelect.innerHTML = '<option value="" data-i18n="please_select">请选择</option>';

        // 添加支出类别（预算通常只针对支出）
        const expenseCategories = [
            '储蓄', '固定', '流动', '债务'
        ];

        expenseCategories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categorySelect.appendChild(option);
        });
    }
}

/**
 * 初始化预算年份选择框
 */
function initBudgetYearSelector() {
    const yearSelect = document.getElementById('budgetYear');

    if (yearSelect && !yearSelect.options.length) {
        const currentYear = new Date().getFullYear();

        // 添加当前年份前后5年的选项
        for (let year = currentYear - 5; year <= currentYear + 5; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = `${year}年`;
            yearSelect.appendChild(option);
        }

        // 设置默认值为当前年份
        yearSelect.value = currentYear;
    }
}

/**
 * 更新预算日期范围
 */
function updateBudgetDateRange(period) {
    const startDateInput = document.getElementById('budgetStartDate');
    const endDateInput = document.getElementById('budgetEndDate');
    const specificMonthSelector = document.getElementById('specificMonthSelector');
    const dateRangeSelector = document.getElementById('dateRangeSelector');
    const yearSelect = document.getElementById('budgetYear');
    const monthSelect = document.getElementById('budgetMonth');

    if (!startDateInput || !endDateInput) return;

    const today = new Date();

    // 处理指定月份选择器的显示/隐藏
    if (period === 'specific') {
        // 显示指定月份选择器
        specificMonthSelector.style.display = 'flex';
        // 隐藏日期范围选择器
        dateRangeSelector.style.display = 'none';

        // 确保年份选择器已初始化（应该在 initBudgetYearSelector 中已完成）
        if (!yearSelect.options.length) {
            initBudgetYearSelector();
        }

        // 设置默认月份为当前月份
        monthSelect.value = today.getMonth() + 1;

        // 根据选择的年月设置日期范围（虽然不显示，但仍需设置值）
        const selectedYear = parseInt(yearSelect.value);
        const selectedMonth = parseInt(monthSelect.value) - 1; // JavaScript月份从0开始

        const firstDay = new Date(selectedYear, selectedMonth, 1);
        const lastDay = new Date(selectedYear, selectedMonth + 1, 0);

        startDateInput.value = formatDateForInput(firstDay);
        endDateInput.value = formatDateForInput(lastDay);

        // 添加年月选择器的变更事件
        yearSelect.onchange = monthSelect.onchange = function() {
            const year = parseInt(yearSelect.value);
            const month = parseInt(monthSelect.value) - 1;

            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);

            startDateInput.value = formatDateForInput(firstDay);
            endDateInput.value = formatDateForInput(lastDay);
        };
    } else {
        // 隐藏指定月份选择器
        specificMonthSelector.style.display = 'none';
        // 显示日期范围选择器
        dateRangeSelector.style.display = 'flex';

        if (period === 'monthly') {
            // 月度预算：当月第一天到最后一天
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            startDateInput.value = formatDateForInput(firstDay);
            endDateInput.value = formatDateForInput(lastDay);
        } else if (period === 'yearly') {
            // 年度预算：当年第一天到最后一天
            const firstDay = new Date(today.getFullYear(), 0, 1);
            const lastDay = new Date(today.getFullYear(), 11, 31);

            startDateInput.value = formatDateForInput(firstDay);
            endDateInput.value = formatDateForInput(lastDay);
        }
    }
}

/**
 * 保存预算
 */
function saveBudget() {
    console.log('=== 开始保存预算 ===');

    // 获取表单数据
    const budgetData = {
        category: document.getElementById('budgetCategory').value,
        amount: parseFloat(document.getElementById('budgetAmount').value),
        period: document.getElementById('budgetPeriod').value,
        start_date: document.getElementById('budgetStartDate').value,
        end_date: document.getElementById('budgetEndDate').value,
        currency: document.getElementById('budgetCurrency').value,
        description: document.getElementById('budgetDescription').value || ''
    };

    console.log('表单数据:', budgetData);

    // 检查每个字段的状态
    console.log('字段检查:');
    console.log('- category:', budgetData.category, '(有效:', !!budgetData.category, ')');
    console.log('- amount:', budgetData.amount, '(有效:', !isNaN(budgetData.amount), ')');
    console.log('- period:', budgetData.period, '(有效:', !!budgetData.period, ')');
    console.log('- start_date:', budgetData.start_date, '(有效:', !!budgetData.start_date, ')');
    console.log('- end_date:', budgetData.end_date, '(有效:', !!budgetData.end_date, ')');
    console.log('- currency:', budgetData.currency, '(有效:', !!budgetData.currency, ')');

    // 验证必填字段
    if (!budgetData.category || isNaN(budgetData.amount) || !budgetData.period ||
        !budgetData.start_date || !budgetData.end_date || !budgetData.currency) {
        console.log('验证失败 - 缺少必填字段');
        showNotification('请填写所有必填字段', 'error');
        return;
    }

    // 如果是指定月份，需要验证年月选择器
    if (budgetData.period === 'specific') {
        const yearSelect = document.getElementById('budgetYear');
        const monthSelect = document.getElementById('budgetMonth');

        if (!yearSelect.value || !monthSelect.value) {
            showNotification('指定月份预算需要选择年份和月份', 'error');
            return;
        }

        // 验证描述字段
        if (!budgetData.description) {
            showNotification('指定月份预算需要填写描述', 'error');
            return;
        }
    }

    console.log('验证通过，继续保存...');

    // 检查日期范围
    const startDate = new Date(budgetData.start_date);
    const endDate = new Date(budgetData.end_date);

    if (startDate > endDate) {
        showNotification('开始日期不能晚于结束日期', 'error');
        return;
    }

    // 获取编辑ID（如果有）
    const budgetId = document.getElementById('budgetForm').getAttribute('data-edit-id');
    if (budgetId) {
        budgetData.id = budgetId;
    }

    // 显示加载状态
    const saveButton = document.getElementById('saveBudget');
    const originalText = saveButton.textContent;
    saveButton.textContent = '保存中...';
    saveButton.disabled = true;

    // 发送到服务器
    fetch('/api/budgets', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        },
        body: JSON.stringify(budgetData)
    })
    .then(response => {
        // 检查响应状态
        if (!response.ok) {
            throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        // 恢复按钮状态
        saveButton.textContent = originalText;
        saveButton.disabled = false;

        if (data.success) {
            showNotification('预算保存成功', 'success');
            console.log('预算保存成功，ID:', data.id);

            // 重置表单
            document.getElementById('budgetForm').reset();
            document.getElementById('budgetForm').removeAttribute('data-edit-id');
            document.getElementById('saveBudget').textContent = '保存';

            // 重新设置默认周期为每月
            const periodSelect = document.getElementById('budgetPeriod');
            if (periodSelect) {
                periodSelect.value = 'monthly';
            }

            // 重新初始化日期
            updateBudgetDateRange('monthly');

            // 立即重新加载预算数据，增加重试机制
            console.log('开始重新加载预算数据...');
            let retryCount = 0;
            const maxRetries = 3;

            const reloadBudgets = () => {
                loadBudgets()
                    .then(() => {
                        console.log('预算数据重新加载成功');
                    })
                    .catch(error => {
                        console.error('重新加载预算数据失败:', error);
                        retryCount++;
                        if (retryCount < maxRetries) {
                            console.log(`重试加载预算数据 (${retryCount}/${maxRetries})`);
                            setTimeout(reloadBudgets, 500 * retryCount); // 递增延迟
                        } else {
                            showNotification('预算已保存，但列表刷新失败。请手动刷新页面。', 'warning');
                        }
                    });
            };

            // 短暂延迟后开始加载
            setTimeout(reloadBudgets, 200);
        } else {
            showNotification(`保存失败: ${data.error || '未知错误'}`, 'error');
            console.error('保存预算失败:', data);
        }
    })
    .catch(error => {
        // 恢复按钮状态
        saveButton.textContent = originalText;
        saveButton.disabled = false;

        console.error('保存预算失败:', error);
        showNotification(`保存预算失败: ${error.message || '请稍后重试'}`, 'error');
    });
}

/**
 * 加载预算数据
 */
function loadBudgets() {
    console.log('开始加载预算数据...');
    return fetch('/api/budgets', {
        method: 'GET',
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(budgets => {
            console.log('成功获取预算数据，数量:', budgets.length);
            displayBudgets(budgets);
            return budgets; // 返回数据以便链式调用
        })
        .catch(error => {
            console.error('加载预算数据失败:', error);
            showNotification(`加载预算数据失败: ${error.message || '请稍后重试'}`, 'error');
            throw error; // 重新抛出错误以便上层处理
        });
}

/**
 * 显示预算列表
 */
function displayBudgets(budgets) {
    const budgetsList = document.getElementById('budgetsList');

    if (budgetsList) {
        // 清空现有内容
        budgetsList.innerHTML = '';

        if (budgets.length === 0) {
            // 没有预算记录
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="6" class="empty-message">暂无预算记录</td>`;
            budgetsList.appendChild(emptyRow);
            return;
        }

        // 获取交易数据，用于计算预算进度
        fetch('/api/transactions', {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`获取交易数据失败: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(transactions => {
                console.log('成功获取交易数据，数量:', transactions.length);

                // 添加预算记录
                budgets.forEach(budget => {
                    try {
                        const row = document.createElement('tr');
                        row.setAttribute('data-id', budget.id);

                        // 格式化金额显示
                        const formattedAmount = formatCurrency(budget.amount, budget.currency);

                        // 获取相关交易
                        const relevantTransactions = getBudgetTransactions(budget, transactions);

                        // 计算预算进度
                        const progress = calculateBudgetProgress(budget, transactions);
                        const progressPercentage = Math.min(100, Math.round(progress * 100));
                        const progressClass = progressPercentage >= 100 ? 'progress-danger' :
                                             progressPercentage >= 80 ? 'progress-warning' : 'progress-success';

                        // 进度条容器状态类
                        const containerClass = progressPercentage >= 100 ? 'progress-danger' :
                                              progressPercentage >= 80 ? 'progress-warning' : '';

                        // 格式化日期范围
                        const dateRange = `${formatDate(budget.start_date)} - ${formatDate(budget.end_date)}`;

                        // 计算已使用金额
                        const totalExpense = relevantTransactions.reduce((sum, tx) => sum + parseFloat(tx.amount), 0);
                        const formattedExpense = formatCurrency(totalExpense, budget.currency);

                        // 生成进度条状态文本
                        let statusText = '';
                        if (progressPercentage >= 100) {
                            statusText = '已超支';
                        } else if (progressPercentage >= 80) {
                            statusText = '接近预算';
                        } else {
                            statusText = `${progressPercentage}%`;
                        }

                        // 设置行内容
                        row.innerHTML = `
                            <td class="budget-category">${budget.category}</td>
                            <td class="budget-amount">${formattedAmount}</td>
                            <td class="budget-period">${getPeriodName(budget.period, budget)}</td>
                            <td class="budget-date-range">${dateRange}</td>
                            <td class="budget-description">${budget.description || ''}</td>
                            <td class="budget-progress">
                                <div class="progress-container ${containerClass}">
                                    <div class="progress-bar ${progressClass}" style="width: ${progressPercentage}%"></div>
                                    <span class="progress-text">${statusText}</span>
                                </div>
                                <div class="progress-amount">${formattedExpense} / ${formattedAmount}</div>
                            </td>
                            <td class="budget-actions">
                                <button class="action-btn edit-btn" data-id="${budget.id}" title="编辑">
                                    <span class="action-icon">✏️</span>
                                </button>
                                <button class="action-btn delete-btn" data-id="${budget.id}" title="删除">
                                    <span class="action-icon">🗑️</span>
                                </button>
                            </td>
                        `;

                        budgetsList.appendChild(row);
                        console.log('成功添加预算行:', budget.category);
                    } catch (error) {
                        console.error('处理预算记录时出错:', budget, error);
                        // 即使单个预算处理失败，也继续处理其他预算
                    }
                });

                // 添加预算按钮的事件监听
                addBudgetButtonListeners();
                console.log('预算列表显示完成，共', budgets.length, '个预算');
            })
            .catch(error => {
                console.error('获取交易数据失败:', error);

                // 即使获取交易数据失败，也要显示预算基本信息
                console.log('交易数据获取失败，显示预算基本信息');
                budgets.forEach(budget => {
                    try {
                        const row = document.createElement('tr');
                        row.setAttribute('data-id', budget.id);

                        // 格式化金额显示
                        const formattedAmount = formatCurrency(budget.amount, budget.currency);

                        // 格式化日期范围
                        const dateRange = `${formatDate(budget.start_date)} - ${formatDate(budget.end_date)}`;

                        // 设置行内容（无进度信息）
                        row.innerHTML = `
                            <td class="budget-category">${budget.category}</td>
                            <td class="budget-amount">${formattedAmount}</td>
                            <td class="budget-period">${getPeriodName(budget.period, budget)}</td>
                            <td class="budget-date-range">${dateRange}</td>
                            <td class="budget-description">${budget.description || ''}</td>
                            <td class="budget-progress">
                                <div class="progress-container">
                                    <div class="progress-bar progress-unknown" style="width: 20%"></div>
                                    <span class="progress-text">加载中...</span>
                                </div>
                                <div class="progress-amount">数据加载失败</div>
                            </td>
                            <td class="budget-actions">
                                <button class="action-btn edit-btn" data-id="${budget.id}" title="编辑">
                                    <span class="action-icon">✏️</span>
                                </button>
                                <button class="action-btn delete-btn" data-id="${budget.id}" title="删除">
                                    <span class="action-icon">🗑️</span>
                                </button>
                            </td>
                        `;

                        budgetsList.appendChild(row);
                    } catch (rowError) {
                        console.error('创建预算行时出错:', budget, rowError);
                    }
                });

                // 添加预算按钮的事件监听
                addBudgetButtonListeners();

                // 显示更友好的错误提示
                showNotification('预算数据已加载，但无法计算进度。请检查网络连接。', 'warning');
            });
    }
}

/**
 * 计算预算进度
 */
function calculateBudgetProgress(budget, transactions) {
    try {
        // 验证输入参数
        if (!budget || !transactions || !Array.isArray(transactions)) {
            console.warn('预算进度计算：无效的输入参数', { budget, transactions });
            return 0;
        }

        // 验证预算数据
        if (!budget.start_date || !budget.end_date || !budget.category || !budget.amount) {
            console.warn('预算进度计算：预算数据不完整', budget);
            return 0;
        }

        // 过滤出在预算日期范围内的交易
        const startDate = new Date(budget.start_date);
        const endDate = new Date(budget.end_date);

        // 验证日期
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            console.warn('预算进度计算：无效的日期格式', { start_date: budget.start_date, end_date: budget.end_date });
            return 0;
        }

        // 过滤出支出类型、匹配类别、在日期范围内的交易
        const relevantTransactions = transactions.filter(tx => {
            try {
                if (!tx || !tx.date || !tx.type || !tx.category) {
                    return false;
                }

                const txDate = new Date(tx.date);
                if (isNaN(txDate.getTime())) {
                    return false;
                }

                return tx.type === 'expense' &&
                       tx.category === budget.category &&
                       txDate >= startDate &&
                       txDate <= endDate;
            } catch (e) {
                console.warn('预算进度计算：处理交易时出错', tx, e);
                return false;
            }
        });

        // 计算总支出
        const totalExpense = relevantTransactions.reduce((sum, tx) => {
            try {
                const amount = parseFloat(tx.amount);
                return sum + (isNaN(amount) ? 0 : amount);
            } catch (e) {
                console.warn('预算进度计算：解析金额时出错', tx.amount, e);
                return sum;
            }
        }, 0);

        // 计算进度（支出/预算）
        const budgetAmount = parseFloat(budget.amount);
        if (isNaN(budgetAmount) || budgetAmount <= 0) {
            console.warn('预算进度计算：无效的预算金额', budget.amount);
            return 0;
        }

        return totalExpense / budgetAmount;
    } catch (error) {
        console.error('预算进度计算时出错:', error, { budget, transactions });
        return 0;
    }
}

/**
 * 获取预算相关交易
 */
function getBudgetTransactions(budget, transactions) {
    try {
        // 验证输入参数
        if (!budget || !transactions || !Array.isArray(transactions)) {
            console.warn('获取预算交易：无效的输入参数', { budget, transactions });
            return [];
        }

        // 验证预算数据
        if (!budget.start_date || !budget.end_date || !budget.category) {
            console.warn('获取预算交易：预算数据不完整', budget);
            return [];
        }

        // 过滤出在预算日期范围内的交易
        const startDate = new Date(budget.start_date);
        const endDate = new Date(budget.end_date);

        // 验证日期
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            console.warn('获取预算交易：无效的日期格式', { start_date: budget.start_date, end_date: budget.end_date });
            return [];
        }

        // 过滤出支出类型、匹配类别、在日期范围内的交易
        return transactions.filter(tx => {
            try {
                if (!tx || !tx.date || !tx.type || !tx.category) {
                    return false;
                }

                const txDate = new Date(tx.date);
                if (isNaN(txDate.getTime())) {
                    return false;
                }

                return tx.type === 'expense' &&
                       tx.category === budget.category &&
                       txDate >= startDate &&
                       txDate <= endDate;
            } catch (e) {
                console.warn('获取预算交易：处理交易时出错', tx, e);
                return false;
            }
        });
    } catch (error) {
        console.error('获取预算交易时出错:', error, { budget, transactions });
        return [];
    }
}

/**
 * 获取周期名称
 */
function getPeriodName(period, budget) {
    const periodMap = {
        'monthly': '每月',
        'yearly': '每年',
        'specific': '指定月份'
    };

    // 如果是指定月份，显示具体的年月信息
    if (period === 'specific' && budget) {
        const startDate = new Date(budget.start_date);
        const year = startDate.getFullYear();
        const month = startDate.getMonth() + 1;
        return `${year}年${month}月`;
    }

    return periodMap[period] || period;
}

/**
 * 添加预算按钮的事件监听
 */
function addBudgetButtonListeners() {
    // 编辑按钮
    const editButtons = document.querySelectorAll('#budgetsList .edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const budgetId = this.getAttribute('data-id');
            editBudget(budgetId);
        });
    });

    // 删除按钮
    const deleteButtons = document.querySelectorAll('#budgetsList .delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const budgetId = this.getAttribute('data-id');
            deleteBudget(budgetId);
        });
    });
}

/**
 * 编辑预算
 */
function editBudget(budgetId) {
    // 获取预算详情
    fetch(`/api/budgets/${budgetId}`)
        .then(response => response.json())
        .then(budget => {
            // 填充表单
            document.getElementById('budgetCategory').value = budget.category;
            document.getElementById('budgetAmount').value = budget.amount;
            document.getElementById('budgetPeriod').value = budget.period;
            document.getElementById('budgetStartDate').value = budget.start_date;
            document.getElementById('budgetEndDate').value = budget.end_date;
            document.getElementById('budgetCurrency').value = budget.currency;
            document.getElementById('budgetDescription').value = budget.description || '';

            // 根据周期类型更新UI
            updateBudgetDateRange(budget.period);

            // 如果是指定月份，需要设置年月选择器
            if (budget.period === 'specific') {
                const startDate = new Date(budget.start_date);
                const yearSelect = document.getElementById('budgetYear');
                const monthSelect = document.getElementById('budgetMonth');

                // 确保年份选项已加载
                if (!yearSelect.options.length) {
                    const currentYear = new Date().getFullYear();
                    for (let year = currentYear - 5; year <= currentYear + 5; year++) {
                        const option = document.createElement('option');
                        option.value = year;
                        option.textContent = `${year}年`;
                        yearSelect.appendChild(option);
                    }
                }

                // 设置年月值
                yearSelect.value = startDate.getFullYear();
                monthSelect.value = startDate.getMonth() + 1;
            }

            // 保存预算ID，用于更新
            document.getElementById('budgetForm').setAttribute('data-edit-id', budgetId);

            // 更改保存按钮文本
            document.getElementById('saveBudget').textContent = '更新';

            // 滚动到表单
            document.getElementById('budgetForm').scrollIntoView({ behavior: 'smooth' });
        })
        .catch(error => {
            console.error('获取预算详情失败:', error);
            showNotification('获取预算详情失败', 'error');
        });
}

/**
 * 删除预算
 */
function deleteBudget(budgetId) {
    if (confirm('确定要删除这个预算吗？此操作不可撤销。')) {
        fetch(`/api/budgets/${budgetId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('预算删除成功', 'success');
                // 重新加载预算数据
                loadBudgets();
            } else {
                showNotification(`删除失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('删除预算失败:', error);
            showNotification('删除预算失败，请稍后重试', 'error');
        });
    }
}

/**
 * 初始化目标表单
 */
function initGoalForm() {
    const goalForm = document.getElementById('goalForm');

    if (goalForm) {
        // 设置默认目标日期为一年后
        const targetDateInput = document.getElementById('goalTargetDate');
        if (targetDateInput) {
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            targetDateInput.value = oneYearLater.toISOString().split('T')[0];
        }

        // 表单提交事件
        goalForm.addEventListener('submit', function(event) {
            event.preventDefault();
            saveGoal();
        });
    }

    // 初始化后立即更新净资产显示
    updateNetAssetDisplay();
}

/**
 * 保存目标
 */
function saveGoal() {
    // 获取表单数据
    const goalData = {
        name: document.getElementById('goalName').value,
        target_amount: parseFloat(document.getElementById('goalTargetAmount').value),
        current_amount: parseFloat(document.getElementById('goalCurrentAmount').value),
        target_date: document.getElementById('goalTargetDate').value,
        currency: 'MYR', // 固定使用MYR货币
        description: document.getElementById('goalDescription').value
    };

    // 直接从仪表盘获取净资产数据
    fetch('/api/transactions', {
        method: 'GET',
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(transactions => {
            // 计算总收入
            const totalIncome = transactions
                .filter(tx => tx.type === 'income')
                .reduce((sum, tx) => sum + parseFloat(tx.amount), 0);

            // 计算总支出
            const totalExpense = transactions
                .filter(tx => tx.type === 'expense')
                .reduce((sum, tx) => sum + parseFloat(tx.amount), 0);

            // 计算净资产
            const netWorth = totalIncome - totalExpense;

            // 获取已分配金额
            return fetch('/api/goals', {
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(goals => {
                    // 计算已分配金额（当前金额总和）
                    let allocatedAmount = goals.reduce((sum, goal) => {
                        // 排除当前编辑的目标
                        const goalId = document.getElementById('goalForm').getAttribute('data-edit-id');
                        if (goal.id !== goalId) {
                            return sum + parseFloat(goal.current_amount);
                        }
                        return sum;
                    }, 0);

                    // 计算剩余可分配金额
                    const remainingAmount = netWorth - allocatedAmount;

                    // 检查当前目标金额是否超过可分配金额
                    if (goalData.current_amount > remainingAmount) {
                        if (!confirm(`当前金额 ${formatCurrency(goalData.current_amount, goalData.currency)} 超过了可分配金额 ${formatCurrency(remainingAmount, goalData.currency)}，是否继续？`)) {
                            return;
                        }
                    }

                    // 继续保存目标
                    continueToSaveGoal(goalData);
                });
        })
        .catch(error => {
            console.error('获取净资产数据失败:', error);
            // 出错时仍然保存目标
            continueToSaveGoal(goalData);
        });
}

/**
 * 继续保存目标
 */
function continueToSaveGoal(goalData) {

    // 验证必填字段
    if (!goalData.name || isNaN(goalData.target_amount) || isNaN(goalData.current_amount) ||
        !goalData.target_date || !goalData.currency) {
        showNotification('请填写所有必填字段', 'error');
        return;
    }

    // 检查金额
    if (goalData.target_amount <= 0) {
        showNotification('目标金额必须大于0', 'error');
        return;
    }

    if (goalData.current_amount < 0) {
        showNotification('当前金额不能为负数', 'error');
        return;
    }

    // 获取编辑ID（如果有）
    const goalId = document.getElementById('goalForm').getAttribute('data-edit-id');
    if (goalId) {
        goalData.id = goalId;
    }

    // 发送到服务器
    fetch('/api/goals', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(goalData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('目标保存成功', 'success');
            // 重置表单
            document.getElementById('goalForm').reset();
            document.getElementById('goalForm').removeAttribute('data-edit-id');
            document.getElementById('saveGoal').textContent = '保存';

            // 设置默认目标日期为一年后
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            document.getElementById('goalTargetDate').value = oneYearLater.toISOString().split('T')[0];

            // 重新加载目标数据
            loadGoals();

            // 更新净资产显示
            updateNetAssetDisplay();
        } else {
            showNotification(`保存失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('保存目标失败:', error);
        showNotification('保存目标失败，请稍后重试', 'error');
    });
}

/**
 * 加载目标数据
 */
function loadGoals() {
    fetch('/api/goals', {
        method: 'GET',
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(goals => {
            displayGoals(goals);
            // 更新净资产显示
            updateNetAssetDisplay();
        })
        .catch(error => {
            console.error('加载目标数据失败:', error);
            showNotification(`加载目标数据失败: ${error.message || '请稍后重试'}`, 'error');
        });
}

/**
 * 初始化目标页面
 */
function initGoalsPage() {
    // 初始化目标表单
    initGoalForm();

    // 加载目标数据
    loadGoals();

    // 更新净资产显示
    updateNetAssetDisplay();
}





/**
 * 更新净资产显示
 */
function updateNetAssetDisplay() {
    // 直接从仪表盘获取净资产数据
    fetch('/api/transactions', {
        method: 'GET',
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(transactions => {
            // 计算总收入
            const totalIncome = transactions
                .filter(tx => tx.type === 'income')
                .reduce((sum, tx) => sum + parseFloat(tx.amount), 0);

            // 计算总支出
            const totalExpense = transactions
                .filter(tx => tx.type === 'expense')
                .reduce((sum, tx) => sum + parseFloat(tx.amount), 0);

            // 计算净资产
            const netWorth = totalIncome - totalExpense;
            console.log('总净资产:', netWorth);

            // 固定使用MYR货币
            const currency = 'MYR';

            // 获取所有目标
            return fetch('/api/goals', {
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(goals => {
                    // 计算已分配金额（当前金额总和）
                    const allocatedAmount = goals.reduce((sum, goal) => {
                        // 只计算相同货币的目标
                        if (goal.currency === currency) {
                            return sum + parseFloat(goal.current_amount);
                        }
                        return sum;
                    }, 0);
                    console.log('已分配金额:', allocatedAmount);

                    // 计算剩余可分配金额
                    const remainingAmount = netWorth - allocatedAmount;
                    console.log('剩余可分配金额:', remainingAmount);

                    // 更新显示
                    document.getElementById('totalNetAsset').textContent = formatCurrency(netWorth, currency);
                    document.getElementById('allocatedAmount').textContent = formatCurrency(allocatedAmount, currency);
                    document.getElementById('remainingAmount').textContent = formatCurrency(remainingAmount, currency);
                });
        })
        .catch(error => {
            console.error('更新净资产显示失败:', error);

            // 更新显示（使用0作为净资产和已分配金额）
            document.getElementById('totalNetAsset').textContent = formatCurrency(0, 'MYR');
            document.getElementById('allocatedAmount').textContent = formatCurrency(0, 'MYR');
            document.getElementById('remainingAmount').textContent = formatCurrency(0, 'MYR');
        });
}

/**
 * 显示目标列表
 */
function displayGoals(goals) {
    const activeGoalsList = document.getElementById('activeGoalsList');
    const completedGoalsList = document.getElementById('completedGoalsList');

    if (activeGoalsList && completedGoalsList) {
        // 清空现有内容
        activeGoalsList.innerHTML = '';
        completedGoalsList.innerHTML = '';

        if (goals.length === 0) {
            // 没有目标记录
            activeGoalsList.innerHTML = '<div class="empty-message">暂无进行中的目标</div>';
            completedGoalsList.innerHTML = '<div class="empty-message">暂无已完成的目标</div>';
            return;
        }

        // 分类目标
        const activeGoals = [];
        const completedGoals = [];

        goals.forEach(goal => {
            const progress = goal.current_amount / goal.target_amount;
            if (progress >= 1) {
                completedGoals.push(goal);
            } else {
                activeGoals.push(goal);
            }
        });

        // 显示进行中的目标
        if (activeGoals.length === 0) {
            activeGoalsList.innerHTML = '<div class="empty-message">暂无进行中的目标</div>';
        } else {
            activeGoals.forEach(goal => {
                const goalCard = createGoalCard(goal);
                activeGoalsList.appendChild(goalCard);
            });
        }

        // 显示已完成的目标
        if (completedGoals.length === 0) {
            completedGoalsList.innerHTML = '<div class="empty-message">暂无已完成的目标</div>';
        } else {
            completedGoals.forEach(goal => {
                const goalCard = createGoalCard(goal);
                completedGoalsList.appendChild(goalCard);
            });
        }

        // 添加目标按钮的事件监听
        addGoalButtonListeners();
    }
}

/**
 * 创建目标卡片
 */
function createGoalCard(goal) {
    const card = document.createElement('div');
    card.className = 'goal-card';
    card.setAttribute('data-id', goal.id);

    // 计算进度
    const progress = goal.current_amount / goal.target_amount;
    const progressPercentage = Math.min(100, Math.round(progress * 100));
    const progressClass = progressPercentage >= 100 ? 'progress-success' :
                         progressPercentage >= 50 ? 'progress-warning' : 'progress-info';

    // 格式化金额
    const formattedTargetAmount = formatCurrency(goal.target_amount, goal.currency);
    const formattedCurrentAmount = formatCurrency(goal.current_amount, goal.currency);

    // 计算剩余天数
    const daysLeft = calculateDaysLeft(goal.target_date);
    const daysLeftText = daysLeft > 0 ? `还剩 ${daysLeft} 天` : '已过期';

    // 设置卡片内容
    card.innerHTML = `
        <div class="goal-header">
            <div class="goal-icon">${goal.icon || '🎯'}</div>
            <div class="goal-title">${goal.name}</div>
            <div class="goal-actions">
                <button class="action-btn edit-btn" data-id="${goal.id}" title="编辑">
                    <span class="action-icon">✏️</span>
                </button>
                <button class="action-btn delete-btn" data-id="${goal.id}" title="删除">
                    <span class="action-icon">🗑️</span>
                </button>
            </div>
        </div>
        <div class="goal-body">
            <div class="goal-progress">
                <div class="progress-container">
                    <div class="progress-bar ${progressClass}" style="width: ${progressPercentage}%"></div>
                    <span class="progress-text">${progressPercentage}%</span>
                </div>
            </div>
            <div class="goal-details">
                <div class="goal-amount">
                    <span class="goal-current-amount">${formattedCurrentAmount}</span>
                    <span class="goal-separator">/</span>
                    <span class="goal-target-amount">${formattedTargetAmount}</span>
                </div>
                <div class="goal-date">
                    <span class="goal-target-date">${formatDate(goal.target_date)}</span>
                    <span class="goal-days-left">${daysLeftText}</span>
                </div>
            </div>
            ${goal.description ? `<div class="goal-description">${goal.description}</div>` : ''}
        </div>
    `;

    return card;
}

/**
 * 计算剩余天数
 */
function calculateDaysLeft(targetDate) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const target = new Date(targetDate);
    target.setHours(0, 0, 0, 0);

    const timeDiff = target - today;
    const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

    return daysDiff;
}

/**
 * 添加目标按钮的事件监听
 */
function addGoalButtonListeners() {
    // 编辑按钮
    const editButtons = document.querySelectorAll('.goal-card .edit-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const goalId = this.getAttribute('data-id');
            editGoal(goalId);
        });
    });

    // 删除按钮
    const deleteButtons = document.querySelectorAll('.goal-card .delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const goalId = this.getAttribute('data-id');
            deleteGoal(goalId);
        });
    });
}

/**
 * 编辑目标
 */
function editGoal(goalId) {
    // 获取目标详情
    fetch(`/api/goals/${goalId}`)
        .then(response => response.json())
        .then(goal => {
            // 填充表单
            document.getElementById('goalName').value = goal.name;
            document.getElementById('goalTargetAmount').value = goal.target_amount;
            document.getElementById('goalCurrentAmount').value = goal.current_amount;
            document.getElementById('goalTargetDate').value = goal.target_date;
            document.getElementById('goalDescription').value = goal.description || '';

            // 保存目标ID，用于更新
            document.getElementById('goalForm').setAttribute('data-edit-id', goalId);

            // 更改保存按钮文本
            document.getElementById('saveGoal').textContent = '更新';

            // 滚动到表单
            document.getElementById('goalForm').scrollIntoView({ behavior: 'smooth' });
        })
        .catch(error => {
            console.error('获取目标详情失败:', error);
            showNotification('获取目标详情失败', 'error');
        });
}

/**
 * 删除目标
 */
function deleteGoal(goalId) {
    if (confirm('确定要删除这个目标吗？此操作不可撤销。')) {
        fetch(`/api/goals/${goalId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('目标删除成功', 'success');
                // 重新加载目标数据
                loadGoals();
                // 更新净资产显示
                updateNetAssetDisplay();
            } else {
                showNotification(`删除失败: ${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error('删除目标失败:', error);
            showNotification('删除目标失败，请稍后重试', 'error');
        });
    }
}

/**
 * 初始化设置页面
 */
function initSettingsPage() {
    const settingsContainer = document.querySelector('.settings-container');

    if (settingsContainer) {
        // 直接使用中文文本，无需翻译

        // 创建设置内容
        settingsContainer.innerHTML = `
            <div class="settings-header">
                <div class="settings-title">设置</div>
            </div>

            <div class="settings-section">
                <h3>个人资料</h3>
                <div class="settings-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="settingsName">姓名</label>
                            <input type="text" id="settingsName" placeholder="姓名">
                        </div>
                        <div class="form-group">
                            <label for="settingsEmail">邮箱</label>
                            <input type="email" id="settingsEmail" placeholder="邮箱">
                        </div>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h3>应用设置</h3>
                <div class="settings-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="settingsDefaultCurrency">默认货币</label>
                            <select id="settingsDefaultCurrency">
                                <!-- 货币选项将通过JavaScript动态添加 -->
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h3>数据管理</h3>
                <div class="settings-actions">
                    <button id="exportDataBtn" class="settings-btn">导出数据</button>
                    <button id="importDataBtn" class="settings-btn">导入数据</button>
                    <button id="clearDataBtn" class="settings-btn danger">清除所有数据</button>
                </div>
            </div>

            <div class="settings-section">
                <h3>关于</h3>
                <div class="about-info">
                    <p>钱管家 v1.0.0</p>
                    <p>一款简单易用的个人财务管理工具，帮助您追踪收支、管理预算、设定财务目标。</p>
                </div>
            </div>
        `;

        // 初始化设置表单
        initSettingsForm();

        // 添加设置按钮事件监听
        addSettingsButtonListeners();
    }
}

/**
 * 初始化设置表单
 */
function initSettingsForm() {
    // 加载默认货币选项
    const currencySelect = document.getElementById('settingsDefaultCurrency');
    if (currencySelect) {
        const currencies = [
            { code: 'MYR', name: '马来西亚林吉特 (RM)' },
            { code: 'USD', name: '美元 ($)' }
        ];

        // 清空现有选项
        currencySelect.innerHTML = '';

        // 添加货币选项
        currencies.forEach(currency => {
            const option = document.createElement('option');
            option.value = currency.code;
            option.textContent = currency.name;
            currencySelect.appendChild(option);
        });

        // 默认选择马来西亚林吉特
        currencySelect.value = 'MYR';
    }

    // 加载保存的设置
    loadSettings();
}

/**
 * 加载保存的设置
 */
function loadSettings() {
    // 从localStorage加载设置
    const settings = JSON.parse(localStorage.getItem('financeAppSettings')) || {};

    // 应用设置
    if (settings.name) document.getElementById('settingsName').value = settings.name;
    if (settings.email) document.getElementById('settingsEmail').value = settings.email;
    if (settings.defaultCurrency) document.getElementById('settingsDefaultCurrency').value = settings.defaultCurrency;
}

/**
 * 保存设置
 */
function saveSettings() {
    // 获取设置值
    const settings = {
        name: document.getElementById('settingsName').value,
        email: document.getElementById('settingsEmail').value,
        defaultCurrency: document.getElementById('settingsDefaultCurrency').value
    };

    // 保存到localStorage
    localStorage.setItem('financeAppSettings', JSON.stringify(settings));

    showNotification('设置已保存', 'success');
}





/**
 * 添加设置按钮事件监听
 */
function addSettingsButtonListeners() {
    // 导出数据按钮
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
        exportDataBtn.addEventListener('click', exportData);
    }

    // 导入数据按钮
    const importDataBtn = document.getElementById('importDataBtn');
    if (importDataBtn) {
        importDataBtn.addEventListener('click', importData);
    }

    // 清除数据按钮
    const clearDataBtn = document.getElementById('clearDataBtn');
    if (clearDataBtn) {
        clearDataBtn.addEventListener('click', clearData);
    }

    // 设置表单变更事件
    const settingsInputs = document.querySelectorAll('.settings-form input, .settings-form select');
    settingsInputs.forEach(input => {
        input.addEventListener('change', saveSettings);
    });
}

/**
 * 导出数据
 */
function exportData() {
    // 获取所有数据
    Promise.all([
        fetch('/api/transactions').then(res => res.json()),
        fetch('/api/accounts').then(res => res.json()),
        fetch('/api/budgets').then(res => res.json()),
        fetch('/api/goals').then(res => res.json())
    ])
    .then(([transactions, accounts, budgets, goals]) => {
        // 创建导出数据对象
        const exportData = {
            transactions,
            accounts,
            budgets,
            goals,
            exportDate: new Date().toISOString(),
            version: '1.0.0'
        };

        // 转换为JSON字符串
        const jsonData = JSON.stringify(exportData, null, 2);

        // 创建下载链接
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `finance_data_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();

        // 清理
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 0);

        showNotification('数据导出成功', 'success');
    })
    .catch(error => {
        console.error('导出数据失败:', error);
        showNotification('导出数据失败，请稍后重试', 'error');
    });
}

/**
 * 导入数据
 */
function importData() {
    // 创建文件输入元素
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'application/json';

    // 监听文件选择事件
    fileInput.addEventListener('change', function() {
        if (fileInput.files.length === 0) return;

        const file = fileInput.files[0];
        const reader = new FileReader();

        reader.onload = function(e) {
            try {
                // 解析JSON数据
                const importData = JSON.parse(e.target.result);

                // 验证数据格式
                if (!importData.transactions || !importData.accounts ||
                    !importData.budgets || !importData.goals) {
                    throw new Error('无效的数据格式');
                }

                // 确认导入
                if (confirm(`确定要导入此数据吗？这将覆盖当前的数据。\n\n导出日期: ${new Date(importData.exportDate).toLocaleString()}\n交易数: ${importData.transactions.length}\n账户数: ${importData.accounts.length}\n预算数: ${importData.budgets.length}\n目标数: ${importData.goals.length}`)) {
                    // 发送数据到服务器
                    fetch('/api/migrate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(importData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showNotification('数据导入成功', 'success');
                            // 重新加载所有数据
                            loadTransactions();
                            loadAccounts();
                            loadBudgets();
                            loadGoals();
                        } else {
                            showNotification(`导入失败: ${data.error}`, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('导入数据失败:', error);
                        showNotification('导入数据失败，请稍后重试', 'error');
                    });
                }
            } catch (error) {
                console.error('解析导入数据失败:', error);
                showNotification('无效的数据格式，请确保导入正确的JSON文件', 'error');
            }
        };

        reader.readAsText(file);
    });

    // 触发文件选择对话框
    fileInput.click();
}

/**
 * 清除所有数据
 */
function clearData() {
    if (confirm('确定要清除所有数据吗？此操作不可撤销。')) {
        if (confirm('再次确认：这将删除所有交易、账户、预算和目标数据。确定要继续吗？')) {
            // 清除所有数据
            Promise.all([
                // 删除所有交易
                fetch('/api/transactions')
                    .then(res => res.json())
                    .then(transactions => {
                        const deletePromises = transactions.map(tx =>
                            fetch(`/api/transactions/${tx.id}`, { method: 'DELETE' })
                        );
                        return Promise.all(deletePromises);
                    }),

                // 删除所有预算
                fetch('/api/budgets')
                    .then(res => res.json())
                    .then(budgets => {
                        const deletePromises = budgets.map(budget =>
                            fetch(`/api/budgets/${budget.id}`, { method: 'DELETE' })
                        );
                        return Promise.all(deletePromises);
                    }),

                // 删除所有目标
                fetch('/api/goals')
                    .then(res => res.json())
                    .then(goals => {
                        const deletePromises = goals.map(goal =>
                            fetch(`/api/goals/${goal.id}`, { method: 'DELETE' })
                        );
                        return Promise.all(deletePromises);
                    }),

                // 删除所有非默认账户
                fetch('/api/accounts')
                    .then(res => res.json())
                    .then(accounts => {
                        const deletePromises = accounts
                            .filter(account => account.id !== 'default')
                            .map(account =>
                                fetch(`/api/accounts/${account.id}`, { method: 'DELETE' })
                            );
                        return Promise.all(deletePromises);
                    })
            ])
            .then(() => {
                showNotification('所有数据已清除', 'success');
                // 重新加载所有数据
                loadTransactions();
                loadAccounts();
                loadBudgets();
                loadGoals();
            })
            .catch(error => {
                console.error('清除数据失败:', error);
                showNotification('清除数据失败，请稍后重试', 'error');
            });
        }
    }
}

/**
 * 获取账户类型名称
 */
function getAccountTypeName(type) {
    const typeMap = {
        'bank': '银行账户',
        'cash': '现金',
        'credit': '信用卡'
    };

    return typeMap[type] || type;
}

/**
 * 格式化日期用于输入框
 */
function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

/**
 * 格式化货币
 */
function formatCurrency(amount, currency) {
    const currencySymbols = {
        'MYR': 'RM',
        'CNY': '¥',
        'USD': '$',
        'EUR': '€',
        'GBP': '£',
        'JPY': '¥',
        'KRW': '₩',
        'SGD': 'S$'
    };

    const symbol = currencySymbols[currency] || currency;
    return `${symbol} ${parseFloat(amount).toFixed(2)}`;
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示通知
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

/**
 * 检查交易后的预算情况
 */
function checkBudgetAfterTransaction(transactionData) {
    fetch('/api/budget-check', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(transactionData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.notification) {
            // 显示预算超出弹窗
            showBudgetAlert(data.notification);
            // 保存通知到历史记录
            saveNotificationToHistory(data.notification);
            // 更新通知栏计数
            updateNotificationCount();
        }
    })
    .catch(error => {
        console.error('检查预算失败:', error);
    });
}

/**
 * 显示预算超出弹窗
 */
function showBudgetAlert(notification) {
    // 创建弹窗遮罩
    const overlay = document.createElement('div');
    overlay.className = 'budget-alert-overlay';

    // 创建弹窗内容
    const alertBox = document.createElement('div');
    alertBox.className = `budget-alert ${notification.type}`;

    // 弹窗图标
    const icon = notification.type === 'danger' ? '🚨' : '⚠️';

    // 弹窗内容
    alertBox.innerHTML = `
        <div class="budget-alert-header">
            <span class="budget-alert-icon">${icon}</span>
            <h3 class="budget-alert-title">${notification.type === 'danger' ? '预算超出警告' : '预算预警'}</h3>
        </div>
        <div class="budget-alert-content">
            <p class="budget-alert-message">${notification.message}</p>
            <div class="budget-alert-details">
                <div class="budget-detail">
                    <span class="label">预算类别:</span>
                    <span class="value">${notification.budget.category}</span>
                </div>
                <div class="budget-detail">
                    <span class="label">预算金额:</span>
                    <span class="value">${formatCurrency(notification.budget.amount, notification.budget.currency)}</span>
                </div>
                <div class="budget-detail">
                    <span class="label">已使用:</span>
                    <span class="value">${formatCurrency(notification.usage.total_spent, notification.budget.currency)}</span>
                </div>
                <div class="budget-detail">
                    <span class="label">使用率:</span>
                    <span class="value ${notification.type === 'danger' ? 'danger' : 'warning'}">${Math.round(notification.usage.percentage * 100)}%</span>
                </div>
            </div>
        </div>
        <div class="budget-alert-actions">
            <button class="btn btn-secondary budget-alert-close">知道了</button>
            <button class="btn btn-primary budget-alert-view" data-budget-id="${notification.budget.id}">查看预算</button>
        </div>
    `;

    overlay.appendChild(alertBox);
    document.body.appendChild(overlay);

    // 添加事件监听器
    const closeBtn = alertBox.querySelector('.budget-alert-close');
    const viewBtn = alertBox.querySelector('.budget-alert-view');

    closeBtn.addEventListener('click', closeBudgetAlert);
    viewBtn.addEventListener('click', function() {
        const budgetId = this.getAttribute('data-budget-id');
        viewBudgetDetails(budgetId);
    });

    // 点击遮罩关闭弹窗
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeBudgetAlert();
        }
    });

    // 显示弹窗
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
}

/**
 * 关闭预算弹窗
 */
function closeBudgetAlert() {
    const overlay = document.querySelector('.budget-alert-overlay');
    if (overlay) {
        overlay.classList.remove('show');
        setTimeout(() => {
            overlay.remove();
        }, 300);
    }
}

/**
 * 查看预算详情
 */
function viewBudgetDetails(budgetId) {
    closeBudgetAlert();
    // 切换到预算页面
    const budgetMenuItem = document.querySelector('[data-page="budgetsPage"]');
    if (budgetMenuItem) {
        budgetMenuItem.click();
    }
}

/**
 * 保存通知到历史记录
 */
function saveNotificationToHistory(notification) {
    const historyData = {
        type: 'budget_alert',
        title: notification.type === 'danger' ? '预算超出警告' : '预算预警',
        message: notification.message,
        data: notification,
        created_at: new Date().toISOString()
    };

    fetch('/api/notifications', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(historyData)
    })
    .catch(error => {
        console.error('保存通知历史失败:', error);
    });
}

/**
 * 更新通知栏计数
 */
function updateNotificationCount() {
    fetch('/api/notifications/unread-count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const notificationBtn = document.querySelector('.notification-btn');
                if (notificationBtn) {
                    // 移除现有的计数标记
                    const existingBadge = notificationBtn.querySelector('.notification-badge');
                    if (existingBadge) {
                        existingBadge.remove();
                    }

                    // 如果有未读通知，添加计数标记
                    if (data.count > 0) {
                        const badge = document.createElement('span');
                        badge.className = 'notification-badge';
                        badge.textContent = data.count > 99 ? '99+' : data.count;
                        notificationBtn.appendChild(badge);
                    }
                }
            }
        })
        .catch(error => {
            console.error('更新通知计数失败:', error);
        });
}

/**
 * 初始化通知栏
 */
function initNotificationBar() {
    const notificationBtn = document.querySelector('.notification-btn');
    if (notificationBtn) {
        // 添加点击事件
        notificationBtn.addEventListener('click', showNotificationHistory);

        // 初始化通知计数
        updateNotificationCount();
    }
}

/**
 * 显示通知历史
 */
function showNotificationHistory() {
    // 创建通知历史弹窗
    const overlay = document.createElement('div');
    overlay.className = 'notification-history-overlay';

    const historyBox = document.createElement('div');
    historyBox.className = 'notification-history-box';

    historyBox.innerHTML = `
        <div class="notification-history-header">
            <h3>通知历史</h3>
            <button class="close-btn">×</button>
        </div>
        <div class="notification-history-content">
            <div class="loading">加载中...</div>
        </div>
        <div class="notification-history-footer">
            <button class="btn btn-secondary mark-all-read-btn">全部标记为已读</button>
            <button class="btn btn-danger clear-all-btn">清空所有通知</button>
        </div>
    `;

    overlay.appendChild(historyBox);
    document.body.appendChild(overlay);

    // 添加事件监听器
    const closeBtn = historyBox.querySelector('.close-btn');
    const markAllReadBtn = historyBox.querySelector('.mark-all-read-btn');
    const clearAllBtn = historyBox.querySelector('.clear-all-btn');

    closeBtn.addEventListener('click', closeNotificationHistory);
    markAllReadBtn.addEventListener('click', markAllAsRead);
    clearAllBtn.addEventListener('click', clearAllNotifications);

    // 点击遮罩关闭弹窗
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeNotificationHistory();
        }
    });

    // 显示弹窗
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);

    // 加载通知历史
    loadNotificationHistory();
}

/**
 * 关闭通知历史弹窗
 */
function closeNotificationHistory() {
    const overlay = document.querySelector('.notification-history-overlay');
    if (overlay) {
        overlay.classList.remove('show');
        setTimeout(() => {
            overlay.remove();
        }, 300);
    }
}

/**
 * 加载通知历史
 */
function loadNotificationHistory() {
    fetch('/api/notifications')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotificationHistory(data.notifications);
            } else {
                throw new Error(data.error || '加载通知历史失败');
            }
        })
        .catch(error => {
            console.error('加载通知历史失败:', error);
            const content = document.querySelector('.notification-history-content');
            if (content) {
                content.innerHTML = '<div class="error">加载通知历史失败</div>';
            }
        });
}

/**
 * 显示通知历史列表
 */
function displayNotificationHistory(notifications) {
    const content = document.querySelector('.notification-history-content');
    if (!content) return;

    if (notifications.length === 0) {
        content.innerHTML = '<div class="empty">暂无通知</div>';
        return;
    }

    const notificationsList = notifications.map(notification => {
        const date = new Date(notification.created_at).toLocaleString('zh-CN');
        const readClass = notification.is_read ? 'read' : 'unread';

        return `
            <div class="notification-item ${readClass}" data-id="${notification.id}">
                <div class="notification-item-header">
                    <span class="notification-title">${notification.title}</span>
                    <span class="notification-date">${date}</span>
                </div>
                <div class="notification-item-content">
                    ${notification.message}
                </div>
                ${!notification.is_read ? '<div class="unread-indicator"></div>' : ''}
            </div>
        `;
    }).join('');

    content.innerHTML = notificationsList;

    // 添加点击事件标记为已读
    content.querySelectorAll('.notification-item.unread').forEach(item => {
        item.addEventListener('click', function() {
            markNotificationAsRead(this.dataset.id);
        });
    });
}

/**
 * 标记通知为已读
 */
function markNotificationAsRead(notificationId) {
    fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新UI
            const item = document.querySelector(`[data-id="${notificationId}"]`);
            if (item) {
                item.classList.remove('unread');
                item.classList.add('read');
                const indicator = item.querySelector('.unread-indicator');
                if (indicator) {
                    indicator.remove();
                }
            }
            // 更新通知计数
            updateNotificationCount();
        }
    })
    .catch(error => {
        console.error('标记通知为已读失败:', error);
    });
}

/**
 * 标记所有通知为已读
 */
function markAllAsRead() {
    fetch('/api/notifications/mark-all-read', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 重新加载通知历史
            loadNotificationHistory();
            // 更新通知计数
            updateNotificationCount();
        }
    })
    .catch(error => {
        console.error('标记所有通知为已读失败:', error);
    });
}

/**
 * 清空所有通知
 */
function clearAllNotifications() {
    if (confirm('确定要清空所有通知吗？此操作不可撤销。')) {
        fetch('/api/notifications', {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 重新加载通知历史
                loadNotificationHistory();
                // 更新通知计数
                updateNotificationCount();
            }
        })
        .catch(error => {
            console.error('清空通知失败:', error);
        });
    }
}

/**
 * 检查登录状态
 */
async function checkLoginStatus() {
    try {
        const response = await fetch('/api/auth/check');
        const result = await response.json();

        if (result.success && result.logged_in) {
            currentUser = result.user;
            return true;
        } else {
            currentUser = null;
            return false;
        }
    } catch (error) {
        console.error('检查登录状态失败:', error);
        currentUser = null;
        return false;
    }
}

/**
 * 初始化用户界面
 */
function initUserInterface() {
    if (currentUser) {
        // 更新用户信息显示
        updateUserDisplay();

        // 添加登出功能
        initLogoutFunction();
    }
}

/**
 * 更新用户信息显示
 */
function updateUserDisplay() {
    // 更新用户头像和名称
    const userAvatar = document.querySelector('.user-avatar');
    const userName = document.querySelector('.user-name');
    const userRole = document.querySelector('.user-role');

    if (userAvatar && currentUser) {
        userAvatar.textContent = currentUser.display_name ? currentUser.display_name.charAt(0).toUpperCase() : 'U';
    }

    if (userName && currentUser) {
        userName.textContent = currentUser.display_name || currentUser.username;
    }

    if (userRole) {
        userRole.textContent = '个人用户';
    }
}

/**
 * 初始化登出功能
 */
function initLogoutFunction() {
    // 为用户头像区域添加点击事件
    const userProfile = document.querySelector('.user-profile');
    if (userProfile) {
        userProfile.style.cursor = 'pointer';
        userProfile.addEventListener('click', showUserMenu);
    }
}

/**
 * 显示用户菜单
 */
function showUserMenu() {
    // 创建用户菜单
    const existingMenu = document.getElementById('userMenu');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }

    const menu = document.createElement('div');
    menu.id = 'userMenu';
    menu.className = 'user-menu';
    menu.innerHTML = `
        <div class="user-menu-item" data-action="profile">
            <i class="fas fa-user"></i> 个人资料
        </div>
        <div class="user-menu-item" data-action="logout">
            <i class="fas fa-sign-out-alt"></i> 退出登录
        </div>
    `;

    // 添加样式
    menu.style.cssText = `
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        z-index: 1000;
        min-width: 150px;
        padding: 8px 0;
    `;

    // 添加菜单项样式
    const style = document.createElement('style');
    style.textContent = `
        .user-menu-item {
            padding: 10px 16px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .user-menu-item:hover {
            background-color: #f5f5f5;
        }
        .user-profile {
            position: relative;
        }
    `;
    document.head.appendChild(style);

    const userProfile = document.querySelector('.user-profile');
    userProfile.appendChild(menu);

    // 添加菜单项点击事件
    menu.addEventListener('click', function(e) {
        const menuItem = e.target.closest('.user-menu-item');
        if (menuItem) {
            const action = menuItem.getAttribute('data-action');
            if (action === 'profile') {
                showUserProfile();
            } else if (action === 'logout') {
                logout();
            }
        }
    });

    // 点击其他地方关闭菜单
    setTimeout(() => {
        document.addEventListener('click', function closeMenu(e) {
            if (!userProfile.contains(e.target)) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        });
    }, 100);
}

/**
 * 显示用户资料
 */
function showUserProfile() {
    const menu = document.getElementById('userMenu');
    if (menu) menu.remove();

    // 创建模态框
    const modal = document.createElement('div');
    modal.id = 'userProfileModal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 30px;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    `;

    modalContent.innerHTML = `
        <div style="text-align: center; margin-bottom: 20px;">
            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white; border-radius: 50%; display: flex; align-items: center;
                        justify-content: center; font-size: 32px; font-weight: bold; margin: 0 auto 15px;">
                ${currentUser.display_name ? currentUser.display_name.charAt(0).toUpperCase() : 'U'}
            </div>
            <h2 style="margin: 0; color: #333;">个人资料</h2>
        </div>

        <div style="margin-bottom: 15px;">
            <label style="display: block; font-weight: bold; color: #666; margin-bottom: 5px;">用户名</label>
            <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e1e5e9;">
                ${currentUser.username}
            </div>
        </div>

        <div style="margin-bottom: 15px;">
            <label style="display: block; font-weight: bold; color: #666; margin-bottom: 5px;">显示名称</label>
            <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e1e5e9;">
                ${currentUser.display_name || currentUser.username}
            </div>
        </div>

        <div style="margin-bottom: 20px;">
            <label style="display: block; font-weight: bold; color: #666; margin-bottom: 5px;">邮箱</label>
            <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e1e5e9;">
                ${currentUser.email}
            </div>
        </div>

        <div style="text-align: center;">
            <button onclick="closeUserProfileModal()" style="
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 8px;
                font-size: 16px;
                cursor: pointer;
                transition: transform 0.2s;
            " onmouseover="this.style.transform='translateY(-2px)'"
               onmouseout="this.style.transform='translateY(0)'">
                关闭
            </button>
        </div>
    `;

    modal.appendChild(modalContent);
    document.body.appendChild(modal);

    // 点击背景关闭模态框
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeUserProfileModal();
        }
    });
}

/**
 * 关闭用户资料模态框
 */
function closeUserProfileModal() {
    const modal = document.getElementById('userProfileModal');
    if (modal) {
        modal.remove();
    }
}

// 将函数设为全局，以便onclick可以访问
window.closeUserProfileModal = closeUserProfileModal;

/**
 * 用户登出
 */
async function logout() {
    const menu = document.getElementById('userMenu');
    if (menu) menu.remove();

    if (!confirm('确定要退出登录吗？')) {
        return;
    }

    try {
        const response = await fetch('/api/auth/logout', {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showNotification('已成功退出登录', 'success');
            setTimeout(() => {
                window.location.href = '/templates/auth/login.html';
            }, 1000);
        } else {
            showNotification('退出登录失败', 'error');
        }
    } catch (error) {
        console.error('退出登录失败:', error);
        showNotification('退出登录失败', 'error');
    }
}
